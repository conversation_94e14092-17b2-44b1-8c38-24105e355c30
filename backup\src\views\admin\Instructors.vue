<template>
  <div class="admin-instructors">
    <div class="header">
      <h1>Instructor Management</h1>
      <div class="filters">
        <select v-model="statusFilter" class="filter-select">
          <option value="all">All Instructors</option>
          <option value="active">Active</option>
          <option value="pending">Pending Approval</option>
          <option value="inactive">Inactive</option>
        </select>
        <input 
          type="text" 
          v-model="searchQuery" 
          placeholder="Search instructors..." 
          class="search-input"
        >
      </div>
    </div>

    <div class="stats-row">
      <div class="stat-card">
        <h3>Total Instructors</h3>
        <p class="stat-value">{{ stats.total }}</p>
      </div>
      <div class="stat-card">
        <h3>Active Instructors</h3>
        <p class="stat-value">{{ stats.active }}</p>
      </div>
      <div class="stat-card">
        <h3>Pending Applications</h3>
        <p class="stat-value">{{ stats.pending }}</p>
      </div>
      <div class="stat-card">
        <h3>Average Rating</h3>
        <p class="stat-value">{{ stats.avgRating }}/5</p>
      </div>
    </div>

    <div class="instructors-grid">
      <div v-for="instructor in filteredInstructors" :key="instructor.id" class="instructor-card">
        <img :src="instructor.avatar || '/default-avatar.png'" :alt="instructor.name" class="instructor-avatar">
        <div class="instructor-info">
          <h3>{{ instructor.name }}</h3>
          <p class="instructor-title">{{ instructor.specialization }}</p>
          <div class="instructor-stats">
            <span><i class="fas fa-book"></i> {{ instructor.courses }} courses</span>
            <span><i class="fas fa-users"></i> {{ instructor.students }} students</span>
            <span><i class="fas fa-star"></i> {{ instructor.rating }}</span>
          </div>
          <div class="instructor-actions">
            <button @click="viewProfile(instructor.id)" class="view-btn">
              View Profile
            </button>
            <button 
              @click="toggleStatus(instructor.id)" 
              :class="['status-btn', instructor.status]"
            >
              {{ instructor.status === 'active' ? 'Deactivate' : 'Activate' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const statusFilter = ref('all')
const searchQuery = ref('')

const stats = ref({
  total: 45,
  active: 38,
  pending: 7,
  avgRating: 4.5
})

const instructors = ref([
  {
    id: 1,
    name: 'Dr. Sarah Johnson',
    specialization: 'Data Science Expert',
    courses: 12,
    students: 2500,
    rating: 4.8,
    status: 'active',
    avatar: null
  },
  {
    id: 2,
    name: 'Prof. Michael Chen',
    specialization: 'Web Development',
    courses: 8,
    students: 1800,
    rating: 4.6,
    status: 'active',
    avatar: null
  },
  // Add more mock instructors
])

const filteredInstructors = computed(() => {
  return instructors.value
    .filter(instructor => {
      if (statusFilter.value === 'all') return true
      return instructor.status === statusFilter.value
    })
    .filter(instructor => {
      if (!searchQuery.value) return true
      return instructor.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
             instructor.specialization.toLowerCase().includes(searchQuery.value.toLowerCase())
    })
})

const viewProfile = (instructorId) => {
  router.push(`/admin/instructors/${instructorId}`)
}

const toggleStatus = (instructorId) => {
  const instructor = instructors.value.find(i => i.id === instructorId)
  if (instructor) {
    instructor.status = instructor.status === 'active' ? 'inactive' : 'active'
  }
}
</script>

<style scoped>
.admin-instructors {
  padding: 2rem;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  margin-top: 5rem;
}

.filters {
  display: flex;
  gap: 1rem;
}

.stats-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: var(--secondary-black);
  padding: 1.5rem;
  border-radius: 12px;
  text-align: center;
}

.stat-value {
  font-size: 2rem;
  font-weight: 600;
  color: var(--accent-color);
  margin-top: 0.5rem;
}

.instructors-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.instructor-card {
  background: var(--secondary-black);
  border-radius: 12px;
  padding: 1.5rem;
  display: flex;
  gap: 1.5rem;
}

.instructor-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
}

.instructor-info {
  flex: 1;
}

.instructor-title {
  color: var(--text-secondary);
  margin: 0.5rem 0;
}

.instructor-stats {
  display: flex;
  gap: 1rem;
  margin: 1rem 0;
  color: var(--text-secondary);
}

.instructor-actions {
  display: flex;
  gap: 0.5rem;
}

.instructor-actions button {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
}

.view-btn {
  background: var(--accent-color);
  color: var(--primary-black);
}

.status-btn {
  background: var(--success-color);
  color: var(--text-primary);
}

.status-btn.inactive {
  background: var(--warning-color);
}
</style>

