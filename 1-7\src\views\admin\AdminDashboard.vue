<template>
  <div class="admin-layout">
    <!-- Admin Header -->
    <header class="admin-header">
      <div class="header-content">
        <div class="header-left">
          <h2 class="admin-title">DP Dashboard</h2>
        </div>
        
        <div class="header-center">
          <div class="search-container">
            <i class="fas fa-search search-icon"></i>
            <input type="text" placeholder="Search..." class="search-input">
          </div>
        </div>
        
        <div class="header-right">
          <!-- Notifications dropdown -->
          <div class="notifications-dropdown" v-click-outside="closeNotificationsDropdown">
            <button class="notifications-btn" @click="isNotificationsOpen = !isNotificationsOpen">
              <i class="fas fa-bell"></i>
              <span class="notification-badge" v-if="unreadNotifications > 0">{{ unreadNotifications }}</span>
            </button>
            <div class="notifications-menu dropdown-menu" v-if="isNotificationsOpen">
              <div class="dropdown-header">
                <h3>Notifications</h3>
                
              </div>
              <div class="notifications-list">
                <div v-for="notification in notifications" :key="notification.id" 
                     :class="['notification-item', { unread: !notification.read }]">
                  <div class="notification-icon">
                    <i :class="notification.icon"></i>
                  </div>
                  <div class="notification-content">
                    <p class="notification-message">{{ notification.message }}</p>
                    <span class="notification-time">{{ notification.time }}</span>
                  </div>
                </div>
              </div>
              <div class="dropdown-footer">
                <router-link to="/admin/notifications" class="view-all">View all notifications</router-link>
              </div>
            </div>
          </div>
          
          <!-- User profile dropdown -->
          <div class="profile-dropdown" v-click-outside="closeProfileDropdown">
            <button class="profile-btn" @click="isProfileOpen = !isProfileOpen">
              <img src="../../assets/default-avatar2.png" alt="Avatar" class="profile-avatar">
              <span class="profile-name">{{ userStore.getUserProfile.name || 'Admin User' }}</span>
              <i class="fas fa-chevron-down"></i>
            </button>
            <div class="dropdown-menu" v-if="isProfileOpen">
              <div class="dropdown-header">
                <div class="dropdown-user-info">
                  <span class="dropdown-name">{{ userStore.getUserProfile.role || 'Administrator' }}</span>
                  <span class="dropdown-email">{{ userStore.getUserProfile.email || '<EMAIL>' }}</span>
                </div>
              </div>
              <router-link to="/admin/dashboard" class="dropdown-item">
                <i class="fas fa-tachometer-alt"></i> Admin Dashboard
              </router-link>
              <router-link to="/admin/users" class="dropdown-item">
                <i class="fas fa-users"></i> User Management
              </router-link>
              <router-link to="/admin/courses" class="dropdown-item">
                <i class="fas fa-book"></i> Course Management
              </router-link>
              <router-link to="/admin/settings" class="dropdown-item">
                <i class="fas fa-cog"></i> Settings
              </router-link>
              <div class="dropdown-divider"></div>
              <button class="dropdown-item" @click="logout">
                <i class="fas fa-sign-out-alt"></i> Logout
              </button>
            </div>
          </div>
        </div>
      </div>
    </header>

    <div class="dashboard-container">
      <aside class="dashboard-sidebar">
        <nav class="dashboard-nav">
          <router-link to="/admin/dashboard" class="nav-item">
            <i class="fas fa-tachometer-alt"></i>
            <span>Dashboard</span>
          </router-link>
          <router-link to="/admin/courses" class="nav-item">
            <i class="fas fa-book"></i>
            <span>Courses</span>
          </router-link>
          <router-link to="/admin/students" class="nav-item">
            <i class="fas fa-users"></i>
            <span>Students</span>
          </router-link>
          <router-link to="/admin/instructors" class="nav-item">
            <i class="fas fa-chalkboard-teacher"></i>
            <span>Instructors</span>
          </router-link>
          <router-link to="/admin/reports" class="nav-item">
            <i class="fas fa-chart-bar"></i>
            <span>Analytics & Reports</span>
          </router-link>
          <router-link to="/admin/users" class="nav-item">
            <i class="fas fa-users"></i>
            <span>User Management</span>
          </router-link>
          <router-link to="/admin/settings" class="nav-item">
            <i class="fas fa-cog"></i>
            <span>Settings</span>
          </router-link>
         
        </nav>
      </aside>

      <main class="dashboard-main">
        <div class="view-container">
          <div class="view-content">
            <router-view v-if="userStore.isAdmin"></router-view>
            <div v-else class="loading-container">
              <p>Checking permissions...</p>
            </div>
          </div>
          <div class="copyright-text">
            <p>&copy; 2025 Datapundits. All rights reserved.</p>
          </div>
        </div>
      </main>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '../../stores/userStore'

const userStore = useUserStore()
const router = useRouter()

// Check admin status on component mount
onMounted(() => {
  console.log("AdminDashboard mounted, checking admin status")
  console.log("Current user role:", userStore.user.role)
  console.log("Is admin?", userStore.isAdmin)
  
  // Redirect if not admin
  if (!userStore.isAdmin) {
    console.warn("Non-admin user attempting to access admin dashboard")
    router.push('/')
  }
})

const isProfileOpen = ref(false)

const closeProfileDropdown = () => {
  isProfileOpen.value = false
}

const logout = () => {
  userStore.logout()
  router.push('/login')
}

// Add to the script setup sections
const isNotificationsOpen = ref(false)
const unreadNotifications = ref(2)

const notifications = ref([
  {
    id: 1,
    message: 'New course submitted for review',
    time: '5 min ago',
    read: false,
    icon: 'fas fa-book'
  },
  {
    id: 2,
    message: 'New instructor application',
    time: '1 hour ago',
    read: false,
    icon: 'fas fa-user'
  },
  {
    id: 3,
    message: 'System maintenance scheduled',
    time: '2 hours ago',
    read: true,
    icon: 'fas fa-cog'
  }
])

const closeNotificationsDropdown = () => {
  isNotificationsOpen.value = false
}

// Click outside directive
const vClickOutside = {
  mounted(el, binding) {
    el.clickOutsideEvent = (event) => {
      if (!(el === event.target || el.contains(event.target))) {
        binding.value(event)
      }
    }
    document.addEventListener('click', el.clickOutsideEvent)
  },
  unmounted(el) {
    document.removeEventListener('click', el.clickOutsideEvent)
  }
}
</script>

<style scoped>
.admin-layout {
  min-height: 100vh;
  background: var(--primary-black);
  display: flex;
  flex-direction: column;
}

.admin-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 72px;
  background: var(--secondary-black);
  border-bottom: 1px solid var(--tertiary-black);
  z-index: 10000;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 2rem;
}

.header-left, .header-right {
  display: flex;
  align-items: center;
}

.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
  padding: 0 2rem;
}

.search-container {
  position: relative;
  width: 100%;
  max-width: 500px;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border-radius: 8px;
  border: 1px solid var(--tertiary-black);
  background: var(--tertiary-black);
  color: var(--text-primary);
  font-size: 0.9rem;
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
}

.notifications-dropdown {
  position: relative;
  margin-right: 1.5rem;
}

.notifications-btn {
  position: relative;
  background: none;
  border: none;
  color: var(--text-primary);
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.notifications-btn:hover {
  background-color: var(--tertiary-black);
}

.notification-badge {
  position: absolute;
  top: 0;
  right: 0;
  background: var(--accent-color);
  color: var(--primary-black);
  font-size: 0.7rem;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notifications-menu {
  width: 320px;
  max-height: 400px;
  overflow-y: auto;
}

.dropdown-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid var(--tertiary-black);
}

.dropdown-header h3 {
  margin: 0;
  font-size: 1rem;
}

.mark-all-read {
  background: none;
  border: none;
  color: var(--accent-color);
  font-size: 0.8rem;
  cursor: pointer;
}

.notifications-list {
  padding: 0.5rem;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  padding: 0.75rem;
  border-radius: 6px;
  margin-bottom: 0.5rem;
  transition: background-color 0.3s ease;
}

.notification-item:hover {
  background-color: var(--tertiary-black);
}

.notification-item.unread {
  background-color: rgba(var(--accent-color-rgb), 0.1);
}

.notification-icon {
  width: 32px;
  height: 32px;
  background: var(--tertiary-black);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.75rem;
  color: var(--accent-color);
}

.notification-content {
  flex: 1;
}

.notification-message {
  margin: 0 0 0.25rem 0;
  font-size: 0.9rem;
}

.notification-time {
  color: var(--text-secondary);
  font-size: 0.8rem;
}

.dropdown-footer {
  padding: 0.75rem;
  text-align: center;
  border-top: 1px solid var(--tertiary-black);
}

.view-all {
  color: var(--accent-color);
  text-decoration: none;
  font-size: 0.9rem;
}

/* Profile Dropdown Styles */
.profile-dropdown {
  position: relative;
}

.profile-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: none;
  color: var(--text-primary);
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 8px;
  transition: background-color 0.3s ease;
}

.profile-btn:hover {
  background-color: var(--tertiary-black);
}

.profile-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: var(--secondary-black);
  border: 1px solid var(--tertiary-black);
  border-radius: 8px;
  width: 280px;
  padding: 0.5rem;
  margin-top: 0.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  z-index: 1100; /* Increased z-index to be above other elements */
}

.dropdown-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border-bottom: 1px solid var(--tertiary-black);
}

.dropdown-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
}

.dropdown-user-info {
  display: flex;
  flex-direction: column;
}

.dropdown-name {
  color: var(--text-primary);
  font-weight: 500;
}

.dropdown-email {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  color: var(--text-primary);
  text-decoration: none;
  width: 100%;
  border: none;
  background: none;
  cursor: pointer;
  transition: background-color 0.3s ease;
  font-size: 0.9rem;
}

.dropdown-item:hover {
  background-color: var(--tertiary-black);
}

.dropdown-divider {
  height: 1px;
  background-color: var(--tertiary-black);
  margin: 0.5rem 0;
}

/* Dashboard Layout */
.dashboard-container {
  display: flex;
  padding-top: 20px; 
  flex: 1;
}

.dashboard-sidebar {
  background: var(--secondary-black);
  border-right: 1px solid var(--tertiary-black);
  width: 290px;
  position: fixed;
  top: 72px; /* Position below the header */
  left: 0;
  height: calc(100vh - 72px); /* Subtract header height */
  overflow-y: auto;
  align-items: start;
}

.dashboard-nav {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  color: var(--text-primary);
  text-decoration: none;
  border-radius: 6px;
  transition: all 0.3s ease;
  margin-bottom: 0.25rem;
}

.nav-item i {
  width: 24px; /* Fixed width for icons */
  text-align: center;
  font-size: 1.1rem;
  margin-right: 12px; /* Consistent spacing */
}

.nav-item span {
  flex: 1;
  white-space: nowrap;
  word-break: break-word;
}

.nav-item.router-link-active {
  background: var(--accent-color);
  color: var(--primary-black);
}

.nav-item:hover:not(.router-link-active) {
  background: var(--tertiary-black);
}

.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100%;
}

.dashboard-main {
  flex: 1;
  margin-left: 280px; /* Match sidebar width */
  min-height: calc(100vh - 72px); /* Subtract header height */
  display: flex;
  flex-direction: column;
}

.copyright-text {
  background-color: var(--secondary-black);
  border-top: 1px solid var(--tertiary-black);
  padding: 1rem;
  text-align: center;
  margin-top: auto;
}

.copyright-text p {
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin: 0;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .header-content {
    padding: 0 1rem;
  }

  .dashboard-container {
    flex-direction: column;
    padding-top: 64px; /* Slightly smaller for mobile */
  }

  .dashboard-sidebar {
    position: relative;
    width: 100%;
    height: auto;
    top: 0;
    padding: 1rem;
    border-right: none;
    border-bottom: 1px solid var(--tertiary-black);
  }

  .dashboard-nav {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .dashboard-main {
    margin-left: 0;
    width: 100%;
  }

  .view-content {
    padding: 1rem;
  }

  .nav-item {
    flex: 1;
    min-width: 120px;
    justify-content: center;
    text-align: center;
    padding: 0.75rem 0.5rem;
  }

  .nav-item i {
    margin: 0 auto;
    font-size: 1.2rem;
    display: block;
    width: 100%;
    margin-bottom: 0.5rem;
  }

  .nav-item span {
    font-size: 0.8rem;
    display: block;
    width: 100%;
  }
}

/* For very small screens */
@media (max-width: 480px) {
  .dashboard-nav {
    justify-content: center;
  }
  
  .nav-item {
    min-width: 80px;
    padding: 0.5rem;
  }
  
  .nav-item span {
    font-size: 0.7rem;
  }
}

/* Mobile responsiveness for header */
@media (max-width: 768px) {
  .header-content {
    flex-wrap: wrap;
    padding: 0.5rem 1rem;
  }
  
  .header-center {
    order: 3;
    width: 100%;
    padding: 0.5rem 0;
  }
  
  .header-left, .header-right {
    width: auto;
  }
  
  .profile-name {
    display: none;
  }
}
</style>






























