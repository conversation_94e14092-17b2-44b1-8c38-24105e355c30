from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import Notification

User = get_user_model()

class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'username', 'email']

class NotificationSerializer(serializers.ModelSerializer):
    time = serializers.SerializerMethodField()
    
    class Meta:
        model = Notification
        fields = ['id', 'notification_type', 'title', 'message', 'icon', 'read', 'time']
        
    def get_time(self, obj):
        return obj.created_at.strftime('%Y-%m-%d %H:%M:%S')

class AdminNotificationSerializer(serializers.ModelSerializer):
    time = serializers.SerializerMethodField()
    user = UserSerializer()
    
    class Meta:
        model = Notification
        fields = ['id', 'notification_type', 'title', 'message', 'icon', 'read', 'time', 'user']
        
    def get_time(self, obj):
        return obj.created_at.strftime('%Y-%m-%d %H:%M:%S')
