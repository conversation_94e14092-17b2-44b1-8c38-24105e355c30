<template>
  <div class="admin-students">
    <div class="header">
      <h1>Student Management</h1>
      <div class="filters">
        <select v-model="statusFilter" class="filter-select">
          <option value="all">All Students</option>
          <option value="active">Active</option>
          <option value="inactive">Inactive</option>
        </select>
        <input 
          type="text" 
          v-model="searchQuery" 
          placeholder="Search students..." 
          class="search-input"
        >
      </div>
    </div>

    <div class="stats-row">
      <div class="stat-card">
        <h3>Total Students</h3>
        <p class="stat-value">{{ stats.totalStudents }}</p>
      </div>
      <div class="stat-card">
        <h3>Active Learners</h3>
        <p class="stat-value">{{ stats.activeLearners }}</p>
      </div>
      <div class="stat-card">
        <h3>Course Completions</h3>
        <p class="stat-value">{{ stats.completions }}</p>
      </div>
      <div class="stat-card">
        <h3>Avg. Course Progress</h3>
        <p class="stat-value">{{ stats.avgProgress }}%</p>
      </div>
    </div>

    <div class="students-table">
      <table>
        <thead>
          <tr>
            <th>Student</th>
            <th>Enrolled Courses</th>
            <th>Progress</th>
            <th>Last Active</th>
            <th>Status</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="student in filteredStudents" :key="student.id">
            <td>
              <div class="student-info">
                <img :src="student.avatar || '/default-avatar.png'" :alt="student.name" class="student-avatar">
                <div>
                  <div class="student-name">{{ student.name }}</div>
                  <div class="student-email">{{ student.email }}</div>
                </div>
              </div>
            </td>
            <td>{{ student.enrolledCourses }}</td>
            <td>
              <div class="progress-bar">
                <div 
                  class="progress" 
                  :style="{ width: `${student.progress}%` }"
                ></div>
              </div>
              <span class="progress-text">{{ student.progress }}%</span>
            </td>
            <td>{{ formatDate(student.lastActive) }}</td>
            <td>
              <span :class="['status-badge', student.status]">
                {{ student.status }}
              </span>
            </td>
            <td>
              <div class="action-buttons">
                <button @click="viewDetails(student.id)" class="view-btn">
                  <i class="fas fa-eye"></i>
                </button>
                <button 
                  @click="toggleStatus(student.id)" 
                  :class="['status-btn', student.status]"
                >
                  <i class="fas fa-power-off"></i>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const statusFilter = ref('all')
const searchQuery = ref('')

const stats = ref({
  totalStudents: 1234,
  activeLearners: 856,
  completions: 432,
  avgProgress: 67
})

const students = ref([
  {
    id: 1,
    name: 'Alice Johnson',
    email: '<EMAIL>',
    enrolledCourses: 3,
    progress: 75,
    lastActive: '2023-06-15',
    status: 'active',
    avatar: null
  },
  {
    id: 2,
    name: 'Bob Smith',
    email: '<EMAIL>',
    enrolledCourses: 2,
    progress: 45,
    lastActive: '2023-06-14',
    status: 'active',
    avatar: null
  },
  // Add more mock students as needed
])

const filteredStudents = computed(() => {
  return students.value
    .filter(student => {
      if (statusFilter.value === 'all') return true
      return student.status === statusFilter.value
    })
    .filter(student => {
      if (!searchQuery.value) return true
      return student.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
             student.email.toLowerCase().includes(searchQuery.value.toLowerCase())
    })
})

const formatDate = (date) => {
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const viewDetails = (studentId) => {
  // Navigate to student details page
  router.push(`/admin/students/${studentId}`)
}

const toggleStatus = (studentId) => {
  const student = students.value.find(s => s.id === studentId)
  if (student) {
    student.status = student.status === 'active' ? 'inactive' : 'active'
  }
}
</script>

<style scoped>
.admin-students {
  padding: 2rem;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  margin-top: 5rem;
}

h1 {
  margin: 0;
  font-size: 1.75rem;
  font-weight: 600;
}

.filters {
  display: flex;
  gap: 1rem;
}

.filter-select,
.search-input {
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background: var(--secondary-black);
  color: var(--text-primary);
}

.stats-row {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: var(--secondary-black);
  padding: 1.5rem;
  border-radius: 8px;
  text-align: center;
}

.stat-value {
  font-size: 2rem;
  font-weight: bold;
  color: var(--accent-color);
  margin-top: 0.5rem;
}

.students-table {
  background: var(--secondary-black);
  border-radius: 8px;
  overflow: hidden;
}

table {
  width: 100%;
  border-collapse: collapse;
}

th, td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

th {
  background: var(--tertiary-black);
  font-weight: 600;
}

.student-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.student-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.student-name {
  font-weight: 500;
}

.student-email {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.progress-bar {
  height: 8px;
  background: var(--tertiary-black);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.25rem;
}

.progress {
  height: 100%;
  background: var(--accent-color);
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.status-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.9rem;
  text-transform: capitalize;
}

.status-badge.active {
  background: rgba(0, 255, 148, 0.1);
  color: #00FF94;
}

.status-badge.inactive {
  background: rgba(255, 69, 58, 0.1);
  color: #FF453A;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
}

.view-btn,
.status-btn {
  padding: 0.5rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: opacity 0.3s;
}

.view-btn {
  background: var(--tertiary-black);
  color: var(--text-primary);
}

.status-btn.active {
  background: rgba(255, 69, 58, 0.1);
  color: #FF453A;
}

.status-btn.inactive {
  background: rgba(0, 255, 148, 0.1);
  color: #00FF94;
}

.view-btn:hover,
.status-btn:hover {
  opacity: 0.8;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .admin-students {
    padding: 1rem;
  }
  
  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .filters {
    width: 100%;
  }
}
</style>

