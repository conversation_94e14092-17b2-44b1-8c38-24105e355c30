from django.shortcuts import render
from rest_framework import viewsets, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.contrib.auth import get_user_model
from .models import Notification
from .serializers import NotificationSerializer, AdminNotificationSerializer
from .utils import send_live_class_notification, send_course_update, send_private_message, send_grade_update

User = get_user_model()

class NotificationViewSet(viewsets.ModelViewSet):
    serializer_class = NotificationSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return Notification.objects.filter(user=self.request.user)
    
    @action(detail=False, methods=['get'])
    def admin(self, request):
        """Get all notifications for admin view"""
        if not request.user.is_staff:
            return Response({'error': 'Permission denied'}, status=403)
            
        notifications = Notification.objects.all().order_by('-created_at')[:100]  # Limit to recent 100
        serializer = AdminNotificationSerializer(notifications, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['post'])
    def mark_all_read(self, request):
        self.get_queryset().update(read=True)
        return Response({'status': 'success'})
    
    @action(detail=True, methods=['post'])
    def mark_read(self, request, pk=None):
        notification = self.get_object()
        notification.read = True
        notification.save()
        return Response({'status': 'success'})
    
    @action(detail=False, methods=['post'])
    def send_live_class(self, request):
        if not request.user.is_staff:
            return Response({'error': 'Permission denied'}, status=403)
            
        course_id = request.data.get('course_id')
        title = request.data.get('title')
        message = request.data.get('message')
        
        send_live_class_notification(course_id, title, message)
        return Response({'status': 'notification sent'})
    
    @action(detail=False, methods=['post'])
    def send_course_update(self, request):
        if not request.user.is_staff:
            return Response({'error': 'Permission denied'}, status=403)
            
        course_id = request.data.get('course_id')
        title = request.data.get('title')
        message = request.data.get('message')
        
        send_course_update(course_id, title, message)
        return Response({'status': 'notification sent'})
        
    @action(detail=False, methods=['post'])
    def send_private_message(self, request):
        if not request.user.is_staff:
            return Response({'error': 'Permission denied'}, status=403)
            
        user_id = request.data.get('user_id')
        message = request.data.get('message')
        
        try:
            recipient = User.objects.get(id=user_id)
            send_private_message(request.user, recipient, message)
            return Response({'status': 'message sent'})
        except User.DoesNotExist:
            return Response({'error': 'User not found'}, status=404)
            
    @action(detail=False, methods=['post'])
    def send_grade_update(self, request):
        if not request.user.is_staff:
            return Response({'error': 'Permission denied'}, status=403)
            
        user_id = request.data.get('user_id')
        course_id = request.data.get('course_id')
        grade = request.data.get('grade')
        
        try:
            user = User.objects.get(id=user_id)
            # Get course name from course ID (adjust based on your course model)
            course_name = f"Course {course_id}"  # Placeholder
            
            send_grade_update(user, course_name, grade)
            return Response({'status': 'notification sent'})
        except User.DoesNotExist:
            return Response({'error': 'User not found'}, status=404)
