import { ref, computed, watch } from 'vue'
import { useChartStore } from '../stores/chartStore'

export function useCharts(options = {}) {
  const chartStore = useChartStore()
  const timeRange = ref(options.timeRange || '30')
  const autoFetch = options.autoFetch !== false
  
  // Track loading and error states
  const isLoading = computed(() => {
    return Object.values(chartStore.loading).some(state => state === true)
  })
  
  const hasError = computed(() => {
    return Object.values(chartStore.error).some(err => err !== null)
  })
  
  // Computed properties for chart data
  const revenueData = computed(() => chartStore.revenueData)
  const enrollmentData = computed(() => chartStore.enrollmentData)
  const userRegistrationData = computed(() => chartStore.userRegistrationData)
  const courseCompletionData = computed(() => chartStore.courseCompletionData)
  const categoryDistributionData = computed(() => chartStore.categoryDistributionData)
  
  // Method to update time range
  const updateTimeRange = async (range) => {
    timeRange.value = range
    chartStore.setTimeRange(range)
    
    if (autoFetch) {
      await fetchChartData()
    }
  }
  
  // Method to fetch all chart data
  const fetchChartData = async () => {
    try {
      await chartStore.fetchAllChartData()
      return true
    } catch (error) {
      console.error('Error in fetchChartData:', error)
      return false
    }
  }
  
  // Method to fetch specific chart data
  const fetchSpecificChart = async (chartType) => {
    try {
      switch (chartType) {
        case 'revenue':
          await chartStore.fetchRevenueData()
          break
        case 'enrollment':
          await chartStore.fetchEnrollmentData()
          break
        case 'userRegistration':
          await chartStore.fetchUserRegistrationData()
          break
        case 'courseCompletion':
          await chartStore.fetchCourseCompletionData()
          break
        case 'categoryDistribution':
          await chartStore.fetchCategoryDistributionData()
          break
        default:
          console.warn(`Unknown chart type: ${chartType}`)
      }
      return true
    } catch (error) {
      console.error(`Error fetching ${chartType} chart:`, error)
      return false
    }
  }
  
  // Get loading state for a specific chart
  const getLoadingState = (chartType) => {
    return chartStore.loading[chartType] || false
  }
  
  // Get error state for a specific chart
  const getErrorState = (chartType) => {
    return chartStore.error[chartType] || null
  }
  
  // Watch for time range changes
  watch(timeRange, (newRange) => {
    chartStore.setTimeRange(newRange)
  })
  
  // Fetch data initially if autoFetch is true
  if (autoFetch) {
    fetchChartData()
  }
  
  // Add this function to generate theme-consistent colors
  const generateChartColors = (count) => {
    const baseColors = [
      'rgba(5, 184, 58, 0.8)',    // Primary accent (green)
      'rgba(64, 145, 247, 0.8)',  // Blue
      'rgba(255, 159, 64, 0.8)',  // Orange
      'rgba(255, 99, 132, 0.8)',  // Red
      'rgba(153, 102, 255, 0.8)', // Purple
      'rgba(255, 206, 86, 0.8)',  // Yellow
      'rgba(75, 192, 192, 0.8)',  // Cyan
      'rgba(153, 102, 255, 0.8)', // Purple
      'rgba(255, 159, 64, 0.8)',  // Orange
      'rgba(255, 99, 132, 0.8)',  // Red
    ]
    return baseColors.slice(0, count)
  }

  return {
    timeRange,
    isLoading,
    hasError,
    revenueData,
    enrollmentData,
    userRegistrationData,
    courseCompletionData,
    categoryDistributionData,
    updateTimeRange,
    fetchChartData,
    fetchSpecificChart,
    getLoadingState,
    getErrorState,
    generateChartColors
  }
}


