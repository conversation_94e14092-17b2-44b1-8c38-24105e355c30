import axios from 'axios'

const API_URL = import.meta.env.VITE_API_URL || 'http://127.0.0.1:8000/'

const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
    'X-Requested-With': 'XMLHttpRequest'
  },
  withCredentials: true 
})

// Add request interceptor
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Add response interceptor for token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config
    
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true
      
      try {
        const refreshToken = localStorage.getItem('refresh_token')
        const response = await axios.post(`${API_URL}api/auth/refresh/`, {
          refresh: refreshToken
        })
        
        const { access } = response.data
        localStorage.setItem('access_token', access)
        
        originalRequest.headers.Authorization = `Bearer ${access}`
        return api(originalRequest)
      } catch (refreshError) {
        // If refresh fails, logout user
        authService.logout()
        window.location.href = '/login'
        return Promise.reject(refreshError)
      }
    }
    
    return Promise.reject(error)
  }
)

export const authService = {
  async login(credentials) {
    try {
      const response = await api.post('api/auth/login/', credentials)
      
      // Debug log the raw response
      console.log('Raw login response:', response.data)
      
      // Extract user data from the nested structure
      const userData = response.data.user
      
      // Debug log the extracted user data
      console.log('Extracted user data:', userData)

      // Validate response data
      if (!userData) {
        throw new Error('Invalid response: missing user data')
      }

      // Transform the data to match our frontend structure
      const transformedUser = {
        id: userData.id,
        email: userData.email,
        name: userData.name,
        role: userData.role,
        avatar: userData.avatar || null,
        bio: userData.bio || '',
        joinedDate: userData.date_joined || new Date().toISOString(),
        isAuthenticated: true
      }

      // Debug log the transformed user
      console.log('Transformed user:', transformedUser)

      return {
        user: transformedUser,
        access: response.data.access,
        refresh: response.data.refresh
      }
    } catch (error) {
      console.error('Login error:', error)
      throw error
    }
  },

  async register(userData) {
    try {
      const response = await api.post('api/auth/register/', userData)
      
      // Debug log the raw response
      console.log('Raw register response:', response.data)
      
      // Extract user data from the nested structure
      const userDataFromResponse = response.data.user
      
      // Debug log the extracted user data
      console.log('Extracted user data:', userDataFromResponse)

      // Validate response data
      if (!userDataFromResponse) {
        throw new Error('Invalid response: missing user data')
      }

      // Transform the data to match our frontend structure
      const transformedUser = {
        id: userDataFromResponse.id,
        email: userDataFromResponse.email,
        name: userDataFromResponse.name,
        role: userDataFromResponse.role,
        avatar: userDataFromResponse.avatar || null,
        bio: userDataFromResponse.bio || '',
        joinedDate: userDataFromResponse.date_joined || new Date().toISOString(),
        isAuthenticated: true
      }

      // Debug log the transformed user
      console.log('Transformed user:', transformedUser)

      return {
        user: transformedUser,
        access: response.data.access,
        refresh: response.data.refresh
      }
    } catch (error) {
      console.error('Registration error:', error)
      throw error
    }
  },

  async logout() {
    try {
      await api.post('api/auth/logout/')
      localStorage.removeItem('token')
      localStorage.removeItem('access_token')
      localStorage.removeItem('refresh_token')
      localStorage.removeItem('user')
    } catch (error) {
      console.error('Logout error:', error)
      // Clean up local storage even if the API call fails
      localStorage.clear()
    }
  },

  getToken() {
    return localStorage.getItem('token') || localStorage.getItem('access_token')
  },

  isAuthenticated() {
    return !!this.getToken()
  }
}









