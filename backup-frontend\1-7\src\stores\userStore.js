import { defineStore } from 'pinia'
import { authService } from '../services/authService'

export const useUserStore = defineStore('user', {
  state: () => ({
    user: JSON.parse(localStorage.getItem('user')) || {
      id: null,
      name: null,
      email: null,
      role: null,
      avatar: null,
      bio: '',
      joinedDate: null,
      isAuthenticated: false
    },
    isLoading: false
  }),

  getters: {
    getUserProfile: (state) => state.user,
    isAuthenticated: (state) => Boolean(state.user.isAuthenticated),
    isAdmin: (state) => {
      console.log("Checking isAdmin, role:", state.user.role);
      return state.user.role?.toLowerCase() === 'administrator' || 
             state.user.role?.toLowerCase() === 'admin';
    },
    isInstructor: (state) => state.user.role?.toLowerCase() === 'instructor',
    isStudent: (state) => state.user.role?.toLowerCase() === 'student',
    defaultRedirectPath: (state) => {
      const role = state.user.role?.toLowerCase()
      if (role === 'administrator') return '/admin/dashboard'
      if (role === 'instructor') return '/instructor/overview'
      return '/dashboard'
    }
  },

  actions: {
    async initializeAuth() {
      const token = authService.getToken()
      if (!token) {
        this.logout()
        return
      }

      const userData = JSON.parse(localStorage.getItem('user'))
      if (userData) {
        this.setUser(userData)
      } else {
        this.logout()
      }
    },

    setUser(userData) {
      if (!userData) {
        this.logout()
        return
      }

      this.user = {
        id: userData.id,
        name: userData.name,
        email: userData.email,
        role: userData.role,
        avatar: userData.avatar,
        bio: userData.bio || '',
        joinedDate: userData.joinedDate || userData.date_joined,
        isAuthenticated: true
      }
      
      localStorage.setItem('user', JSON.stringify(this.user))
      console.log('Updated user state:', this.user)
    },

    updateProfile(profileData) {
      this.user = {
        ...this.user,
        ...profileData
      }
      localStorage.setItem('user', JSON.stringify(this.user))
    },

    logout() {
      this.user = {
        id: null,
        name: null,
        email: null,
        role: null,
        avatar: null,
        bio: '',
        joinedDate: null,
        isAuthenticated: false
      }
      localStorage.removeItem('user')
      localStorage.removeItem('token')
      localStorage.removeItem('access_token')
      localStorage.removeItem('refresh_token')
    }
  }
})









