from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
import json
from .models import Notification

channel_layer = get_channel_layer()

def send_notification(user, notification_type, title, message, icon='fas fa-bell'):
    """Create and send a notification to a specific user"""
    # Create notification in database
    notification = Notification.objects.create(
        user=user,
        notification_type=notification_type,
        title=title,
        message=message,
        icon=icon
    )
    
    # Format notification for sending
    notification_data = {
        'id': notification.id,
        'type': notification.notification_type,
        'title': notification.title,
        'message': notification.message,
        'time': notification.created_at.strftime('%Y-%m-%d %H:%M:%S'),
        'read': notification.read,
        'icon': notification.icon
    }
    
    # Send to user's channel group
    async_to_sync(channel_layer.group_send)(
        f'user_{user.id}',
        {
            'type': 'notification_message',
            'notification': notification_data
        }
    )
    
    return notification

def send_live_class_notification(course_id, title, message):
    """Send notification to all users in a live class"""
    async_to_sync(channel_layer.group_send)(
        f'live-class-{course_id}',
        {
            'type': 'notification_message',
            'notification': {
                'type': 'live_class',
                'title': title,
                'message': message,
                'time': 'Just now',
                'read': False,
                'icon': 'fas fa-video'
            }
        }
    )

def send_course_update(course_id, title, message):
    """Send notification to all users enrolled in a course"""
    async_to_sync(channel_layer.group_send)(
        f'course-updates-{course_id}',
        {
            'type': 'notification_message',
            'notification': {
                'type': 'course_update',
                'title': title,
                'message': message,
                'time': 'Just now',
                'read': False,
                'icon': 'fas fa-book'
            }
        }
    )

def send_private_message(from_user, to_user, message):
    """Send a private message notification"""
    send_notification(
        to_user,
        'private_message',
        f'Message from {from_user.username}',
        message,
        'fas fa-envelope'
    )

def send_grade_update(user, course_name, grade):
    """Send a grade update notification"""
    send_notification(
        user,
        'grade_update',
        'Grade Updated',
        f'Your grade for {course_name} has been updated to {grade}',
        'fas fa-graduation-cap'
    )