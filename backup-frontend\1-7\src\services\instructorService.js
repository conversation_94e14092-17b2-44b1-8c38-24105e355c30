import apiClient from './apiClient'

// API URL for instructors
const API_URL = '/api/accounts/instructors/'

// Flag to control mock API usage
const USE_MOCK_API = false

export const instructorService = {
  // Get all instructors with optional filters
  async getAllInstructors(filters = {}, page = 1, pageSize = 10) {
    if (USE_MOCK_API) {
      console.log('Using mock API for getAllInstructors')
      return Promise.resolve({ 
        data: [
          {
            id: 1,
            name: '<PERSON>',
            email: '<EMAIL>',
            specialization: 'Web Development',
            is_active: true,
            courses_count: 5,
            students_count: 120,
            average_rating: 4.7
          },
          // Add more mock data as needed
        ]
      })
    }
    
    try {
      // Convert filters object to URL parameters
      const params = new URLSearchParams()
      
      // Handle status filter
      if (filters.status === 'active') {
        params.append('is_active', 'true')
      } else if (filters.status === 'inactive') {
        params.append('is_active', 'false')
      }
      
      // Handle search filter
      if (filters.search) params.append('search', filters.search)
      
      // Add pagination parameters
      params.append('page', page)
      params.append('page_size', pageSize)
      
      const url = `${API_URL}?${params.toString()}`
      console.log('Fetching instructors from URL:', url)
      
      // Set a longer timeout for the request
      const response = await apiClient.get(url, { timeout: 15000 })
      
      // Log the raw response for debugging
      console.log('Raw instructor API response:', response);
      console.log('Instructor API response data:', response.data);
      
      // Ensure we always return a valid response
      if (!response.data) {
        console.warn('Empty response data from instructor API');
        return { data: [] };
      }
      
      return response;
    } catch (error) {
      console.error('Error in getAllInstructors:', error);
      
      // Provide more detailed error information
      if (error.response) {
        console.error('Error response:', error.response.status, error.response.data);
      } else if (error.request) {
        console.error('No response received:', error.request);
      }
      
      throw error;
    }
  },
  
  // Get instructor by ID
  async getInstructorById(id) {
    if (USE_MOCK_API) {
      return Promise.resolve({ 
        data: {
          id: id,
          name: 'John Doe',
          email: '<EMAIL>',
          specialization: 'Web Development',
          is_active: true,
          courses_count: 5,
          students_count: 120,
          average_rating: 4.7,
          bio: 'Experienced web developer with 10+ years of experience',
          courses: [],
          reviews: []
        }
      })
    }
    
    try {
      console.log(`Fetching instructor with ID: ${id}`)
      const response = await apiClient.get(`${API_URL}${id}/`)
      console.log('Instructor details API response:', response.data)
      return response
    } catch (error) {
      console.error(`Error fetching instructor ${id}:`, error)
      throw error
    }
  },
  
  // Get instructor statistics
  async getInstructorStats() {
    if (USE_MOCK_API) {
      return Promise.resolve({
        data: {
          total: 45,
          active: 38,
          pending: 7,
          average_rating: 4.5
        }
      })
    }
    
    try {
      console.log('Fetching instructor stats')
      const response = await apiClient.get(`${API_URL}stats/`)
      
      // Log the response for debugging
      console.log('Instructor stats API response:', response.data)
      
      return response
    } catch (error) {
      console.error('Error fetching instructor stats:', error)
      
      // If the stats endpoint fails, return mock data as fallback
      if (USE_MOCK_API) {
        return Promise.resolve({
          data: {
            total: 45,
            active: 38,
            pending: 7,
            average_rating: 4.5
          }
        })
      }
      
      throw error
    }
  },
  
  // Toggle instructor status (active/inactive)
  async toggleInstructorStatus(instructorId, isActive) {
    if (USE_MOCK_API) {
      return Promise.resolve({ data: { success: true } })
    }
    
    try {
      console.log(`Toggling instructor ${instructorId} status to ${isActive ? 'active' : 'inactive'}`)
      const response = await apiClient.patch(`${API_URL}${instructorId}/`, {
        is_active: isActive
      })
      console.log('Toggle status API response:', response.data)
      return response
    } catch (error) {
      console.error(`Error toggling instructor ${instructorId} status:`, error)
      throw error
    }
  }
}


