import { defineStore } from 'pinia'
import { studentService } from '../services/studentService'

export const useStudentStore = defineStore('student', {
  state: () => ({
    students: [],
    stats: {
      totalStudents: 0,
      activeLearners: 0,
      completions: 0,
      avgProgress: 0
    },
    pagination: {
      currentPage: 1,
      totalPages: 1,
      totalItems: 0,
      pageSize: 10
    },
    loading: false,
    error: null,
    // Add this to track current filters
    currentFilters: {
      status: 'all',
      search: ''
    }
  }),
  
  getters: {
    getFilteredStudents: (state) => (statusFilter, searchQuery) => {
      return state.students.filter(student => {
        // Filter by status
        if (statusFilter !== 'all') {
          const isActive = statusFilter === 'active'
          if (student.status !== (isActive ? 'active' : 'inactive')) return false
        }
        
        // Filter by search query
        if (searchQuery) {
          const query = searchQuery.toLowerCase()
          return student.name.toLowerCase().includes(query) || 
                 student.email.toLowerCase().includes(query)
        }
        
        return true
      })
    }
  },
  
  actions: {
    async fetchStudents(filters = {}, page = 1) {
      this.loading = true
      this.error = null
      
      // Store current filters for reuse
      this.currentFilters = {
        status: filters.status || 'all',
        search: filters.search || ''
      }
      
      try {
        // Add a timeout to prevent infinite loading
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Request timed out')), 15000)
        })
        
        // Race the actual request with the timeout
        const response = await Promise.race([
          studentService.getAllStudents(filters, page, this.pagination.pageSize),
          timeoutPromise
        ])
        
        // Log the response for debugging
        console.log('Student list API response:', response.data)
        
        // Check if response has the expected structure
        if (!response.data || !Array.isArray(response.data.results)) {
          console.error('Unexpected API response format:', response.data)
          throw new Error('Invalid response format from API')
        }
        
        // Map the student data
        this.students = response.data.results.map(student => ({
          id: student.id,
          name: student.name,
          email: student.email,
          enrolledCourses: student.enrolled_courses || 0,
          progress: student.progress || 0,
          lastActive: student.last_active,
          status: student.is_active ? 'active' : 'inactive',
          avatar: student.avatar
        }))
        
        // Update pagination information
        this.pagination = {
          currentPage: page,
          totalPages: Math.ceil(response.data.count / this.pagination.pageSize),
          totalItems: response.data.count,
          pageSize: this.pagination.pageSize
        }
        
        // Update the total students count in stats
        if (response.data.count !== undefined) {
          this.stats.totalStudents = response.data.count
          
          // Calculate active learners count from the fetched data
          const activeLearners = this.students.filter(s => s.status === 'active').length
          
          // Only update activeLearners if we're on the first page or if we have all students
          if (page === 1 || this.students.length === response.data.count) {
            this.stats.activeLearners = activeLearners
          } else {
            // If we're not on the first page and don't have all students,
            // we need to calculate the proportion of active students on this page
            // and estimate the total
            const activeRatio = activeLearners / this.students.length
            this.stats.activeLearners = Math.round(activeRatio * response.data.count)
          }
        }
        
        return this.students
      } catch (error) {
        this.error = error.message || 'Failed to fetch students'
        console.error('Error fetching students:', error)
        
        // Initialize empty array to prevent undefined errors
        this.students = []
        
        throw error
      } finally {
        // Always set loading to false, even on error
        this.loading = false
      }
    },
    
    async changePage(newPage) {
      if (newPage < 1 || newPage > this.pagination.totalPages) return
      
      // Update current page immediately for UI feedback
      this.pagination.currentPage = newPage
      
      // Fetch the new page data using the stored filters
      await this.fetchStudents(this.currentFilters, newPage)
    },
    
    async fetchStudentStats() {
      // Don't set loading to true here to avoid UI flicker
      // when both fetchStudents and fetchStudentStats are called
      this.error = null
      
      try {
        const response = await studentService.getStudentStats()
        
        // Ensure we're getting the data in the expected format
        if (!response.data) {
          throw new Error('Invalid response format from student stats API')
        }
        
        const data = response.data
        
        // Log the parsed data for debugging
        console.log('Processing student stats data:', data)
        
        // Update the stats with proper fallbacks for each field
        // but preserve activeLearners if it's not provided
        const currentActiveLearners = this.stats.activeLearners
        
        this.stats = {
          totalStudents: typeof data.total === 'number' ? data.total : this.stats.totalStudents,
          activeLearners: typeof data.active === 'number' ? data.active : currentActiveLearners,
          completions: typeof data.completions === 'number' ? data.completions : this.stats.completions,
          avgProgress: typeof data.avg_progress === 'number' ? data.avg_progress : this.stats.avgProgress
        }
        
        // Log the final stats object
        console.log('Updated stats in store:', this.stats)
        
        return this.stats
      } catch (error) {
        console.error('Error in fetchStudentStats:', error)
        this.error = error.message || 'Failed to fetch student statistics'
        
        // Don't reset stats to zero if there's an error - keep the last known values
        
        throw error
      }
    },
    
    async toggleStudentStatus(studentId) {
      const student = this.students.find(s => s.id === studentId)
      if (!student) {
        console.error('Student not found in store:', studentId)
        throw new Error('Student not found')
      }
      
      // Store the current status
      const currentStatus = student.status
      
      // Optimistically update the UI
      const newStatus = currentStatus === 'active' ? 'inactive' : 'active'
      student.status = newStatus
      
      try {
        // Convert status string to boolean for API
        const isActive = newStatus === 'active'
        
        console.log(`Calling API to toggle student ${studentId} status to ${isActive ? 'active' : 'inactive'}`)
        
        // Call the API
        const response = await studentService.toggleStudentStatus(studentId, isActive)
        
        console.log('Toggle status API response:', response)
        
        // Update active learners count in stats
        if (this.stats) {
          if (newStatus === 'active' && currentStatus === 'inactive') {
            this.stats.activeLearners++
          } else if (newStatus === 'inactive' && currentStatus === 'active') {
            this.stats.activeLearners--
          }
        }
        
        return true
      } catch (error) {
        // Revert the optimistic update on error
        student.status = currentStatus
        console.error('Error toggling student status:', error)
        throw error
      }
    },
    
    async updateStudent(studentData) {
      try {
        console.log('Updating student in store:', studentData)
        
        const response = await studentService.updateStudent(studentData.id, {
          name: studentData.name,
          email: studentData.email,
          is_active: studentData.is_active
        })
        
        console.log('Update student API response:', response.data)
        
        // Update the student in the local state
        const index = this.students.findIndex(s => s.id === studentData.id)
        if (index !== -1) {
          this.students[index] = {
            ...this.students[index],
            name: studentData.name,
            email: studentData.email,
            status: studentData.is_active ? 'active' : 'inactive'
          }
          
          console.log('Updated student in store:', this.students[index])
        } else {
          console.warn('Student not found in store after update:', studentData.id)
        }
        
        return response.data
      } catch (error) {
        console.error('Error updating student:', error)
        throw error
      }
    },
    
    async deleteStudent(studentId) {
      try {
        await studentService.deleteStudent(studentId)
        
        // Remove the student from the local state
        this.students = this.students.filter(s => s.id !== studentId)
        
        // Update stats
        this.stats.totalStudents -= 1
        
        // Refresh the current page if it's now empty (unless it's the first page)
        if (this.students.length === 0 && this.pagination.currentPage > 1) {
          await this.changePage(this.pagination.currentPage - 1)
        }
      } catch (error) {
        console.error('Error deleting student:', error)
        throw error
      }
    },
    
    async updateStudentWithFormData(id, formData) {
      this.loading = true
      this.error = null
      
      try {
        const response = await studentService.updateStudentWithFormData(id, formData)
        
        // Update the student in the local state
        if (response.data) {
          const updatedStudent = {
            id: response.data.id,
            name: response.data.name,
            email: response.data.email,
            status: response.data.is_active ? 'active' : 'inactive',
            avatar: response.data.avatar,
            phone: response.data.phone,
            location: response.data.location,
            bio: response.data.bio,
            enrolledCourses: response.data.enrolled_courses || 0,
            progress: response.data.progress || 0,
            lastActive: response.data.last_active
          }
          
          // Update in the students array if it exists
          const index = this.students.findIndex(s => s.id === id)
          if (index !== -1) {
            this.students[index] = { ...this.students[index], ...updatedStudent }
          }
          
          // Update selectedStudent if it's the same student
          if (this.selectedStudent && this.selectedStudent.id === id) {
            this.selectedStudent = updatedStudent
          }
          
          return updatedStudent
        }
      } catch (error) {
        this.error = error.message || 'Failed to update student'
        console.error('Error updating student with form data:', error)
        throw error
      } finally {
        this.loading = false
      }
    }
  }
})




