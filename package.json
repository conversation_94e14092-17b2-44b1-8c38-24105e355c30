{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@vuelidate/core": "^2.0.3", "@vuelidate/validators": "^2.0.4", "axios": "^1.8.4", "chart.js": "^4.4.9", "pinia": "^3.0.1", "vue": "^3.4.37", "vue-chartjs": "^5.2.0", "vue-router": "^4.4.3"}, "devDependencies": {"@vitejs/plugin-vue": "^5.1.2", "vite": "^5.4.1"}}