import json
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from .models import Notification
from django.contrib.auth import get_user_model

User = get_user_model()

class NotificationConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        self.user = self.scope["user"]
        
        if not self.user.is_authenticated:
            await self.close()
            return
            
        self.user_group_name = f'user_{self.user.id}'
        
        # Join user group
        await self.channel_layer.group_add(
            self.user_group_name,
            self.channel_name
        )
        
        # Join channel groups based on user's roles and enrollments
        if self.user.is_staff or self.user.is_superuser:
            await self.channel_layer.group_add(
                'admin_notifications',
                self.channel_name
            )
            
        # Add user to course groups they're enrolled in
        # This would need to be customized based on your enrollment model
        courses = await self.get_user_courses()
        for course_id in courses:
            await self.channel_layer.group_add(
                f'course-updates-{course_id}',
                self.channel_name
            )
            
        await self.accept()
    
    async def disconnect(self, close_code):
        # Leave all groups
        await self.channel_layer.group_discard(
            self.user_group_name,
            self.channel_name
        )
        
        if self.user.is_staff or self.user.is_superuser:
            await self.channel_layer.group_discard(
                'admin_notifications',
                self.channel_name
            )
            
        courses = await self.get_user_courses()
        for course_id in courses:
            await self.channel_layer.group_discard(
                f'course-updates-{course_id}',
                self.channel_name
            )
    
    async def receive(self, text_data):
        data = json.loads(text_data)
        message_type = data.get('type')
        
        if message_type == 'mark_read':
            notification_id = data.get('notification_id')
            await self.mark_notification_read(notification_id)
            
            # Send confirmation back to user
            await self.send(text_data=json.dumps({
                'type': 'notification_marked_read',
                'notification_id': notification_id
            }))
    
    async def notification_message(self, event):
        # Send notification to WebSocket
        await self.send(text_data=json.dumps({
            'type': 'notification',
            'notification': event['notification']
        }))
    
    @database_sync_to_async
    def get_user_courses(self):
        # Replace with your actual enrollment model query
        # Example: return self.user.enrollments.values_list('course_id', flat=True)
        return [1, 2, 3]  # Placeholder
    
    @database_sync_to_async
    def mark_notification_read(self, notification_id):
        try:
            notification = Notification.objects.get(id=notification_id, user=self.user)
            notification.read = True
            notification.save()
            return True
        except Notification.DoesNotExist:
            return False