<template>
  <div class="instructor-profile">
    <div class="header">
      <button class="back-btn" @click="goBack">
        <i class="fas fa-arrow-left"></i> Back to Instructors
      </button>
      <h1>Instructor Profile</h1>
    </div>

    <div v-if="loading" class="loading-container">
      <div class="spinner"></div>
      <p>Loading instructor profile...</p>
    </div>

    <div v-else-if="!instructor" class="error-container">
      <i class="fas fa-exclamation-circle"></i>
      <p>Instructor not found</p>
      <button class="back-btn" @click="goBack">Return to Instructors</button>
    </div>

    <div v-else class="profile-content">
      <div class="profile-header">
        <img :src="instructor.avatar || '/default-avatar.png'" :alt="instructor.name" class="profile-avatar">
        <div class="profile-info">
          <h2>{{ instructor.name }}</h2>
          <p class="specialization">{{ instructor.specialization }}</p>
          <div class="profile-stats">
            <div class="stat-item">
              <i class="fas fa-book"></i>
              <span>{{ instructor.courses }} courses</span>
            </div>
            <div class="stat-item">
              <i class="fas fa-users"></i>
              <span>{{ instructor.students }} students</span>
            </div>
            <div class="stat-item">
              <i class="fas fa-star"></i>
              <span>{{ instructor.rating }} rating</span>
            </div>
          </div>
          <div class="status-badge" :class="instructor.status">
            {{ instructor.status.charAt(0).toUpperCase() + instructor.status.slice(1) }}
          </div>
        </div>
      </div>

      <div class="profile-tabs">
        <button 
          v-for="tab in tabs" 
          :key="tab.id" 
          :class="['tab-btn', { active: activeTab === tab.id }]"
          @click="activeTab = tab.id"
        >
          {{ tab.name }}
        </button>
      </div>

      <div class="tab-content">
        <!-- Overview Tab -->
        <div v-if="activeTab === 'overview'" class="overview-tab">
          <div class="info-card">
            <h3>About</h3>
            <p>{{ instructor.bio || 'No bio information available.' }}</p>
          </div>
          
          <div class="info-card">
            <h3>Contact Information</h3>
            <div class="contact-info">
              <div class="contact-item">
                <i class="fas fa-envelope"></i>
                <span>{{ instructor.email }}</span>
              </div>
              <div class="contact-item" v-if="instructor.phone">
                <i class="fas fa-phone"></i>
                <span>{{ instructor.phone }}</span>
              </div>
              <div class="contact-item" v-if="instructor.website">
                <i class="fas fa-globe"></i>
                <span>{{ instructor.website }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Courses Tab -->
        <div v-if="activeTab === 'courses'" class="courses-tab">
          <div v-if="!instructor.coursesList || instructor.coursesList.length === 0" class="empty-state">
            <i class="fas fa-book"></i>
            <p>No courses available</p>
          </div>
          <div v-else class="courses-grid">
            <div v-for="(course, index) in instructor.coursesList" :key="index" class="course-card">
              <img :src="course.image || '/default-course.jpg'" :alt="course.title" class="course-image">
              <div class="course-content">
                <h3>{{ course.title }}</h3>
                <p class="course-description">{{ course.description }}</p>
                <div class="course-stats">
                  <span><i class="fas fa-users"></i> {{ course.students }} students</span>
                  <span><i class="fas fa-star"></i> {{ course.rating }}</span>
                </div>
                <button class="view-course-btn">View Course</button>
              </div>
            </div>
          </div>
        </div>

        <!-- Reviews Tab -->
        <div v-if="activeTab === 'reviews'" class="reviews-tab">
          <div v-if="!instructor.reviews || instructor.reviews.length === 0" class="empty-state">
            <i class="fas fa-star"></i>
            <p>No reviews available</p>
          </div>
          <div v-else class="reviews-list">
            <div v-for="(review, index) in instructor.reviews" :key="index" class="review-item">
              <div class="review-header">
                <div class="student-info">
                  <img :src="review.studentAvatar || '/default-avatar.png'" :alt="review.studentName" class="student-avatar">
                  <div>
                    <h4>{{ review.studentName }}</h4>
                    <p class="course-name">{{ review.courseName }}</p>
                  </div>
                </div>
                <div class="review-rating">
                  <div class="stars">{{ '⭐'.repeat(review.rating) }}</div>
                  <div class="date">{{ formatDate(review.date) }}</div>
                </div>
              </div>
              <p class="review-content">{{ review.content }}</p>
            </div>
          </div>
        </div>

        <!-- Actions Tab -->
        <div v-if="activeTab === 'actions'" class="actions-tab">
          <div class="action-card">
            <h3>Account Status</h3>
            <p>Current status: <span :class="['status-text', instructor.status]">{{ instructor.status }}</span></p>
            <button 
              @click="toggleStatus" 
              :class="['status-toggle-btn', instructor.status === 'active' ? 'deactivate' : 'activate']"
            >
              {{ instructor.status === 'active' ? 'Deactivate Account' : 'Activate Account' }}
            </button>
          </div>
          
          <div class="action-card">
            <h3>Admin Actions</h3>
            <div class="admin-actions">
              <button class="action-btn warning">
                <i class="fas fa-envelope"></i> Send Message
              </button>
              <button class="action-btn danger">
                <i class="fas fa-exclamation-triangle"></i> Flag Account
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()
const instructorId = parseInt(route.params.id)
const instructor = ref(null)
const loading = ref(true)
const activeTab = ref('overview')

const tabs = [
  { id: 'overview', name: 'Overview' },
  { id: 'courses', name: 'Courses' },
  { id: 'reviews', name: 'Reviews' },
  { id: 'actions', name: 'Actions' }
]

// Mock data for demonstration
const instructorsData = [
  {
    id: 1,
    name: 'Dr. Sarah Johnson',
    specialization: 'Data Science Expert',
    courses: 12,
    students: 2500,
    rating: 4.8,
    status: 'active',
    avatar: null,
    email: '<EMAIL>',
    phone: '+****************',
    website: 'www.sarahjohnson.com',
    bio: 'Dr. Sarah Johnson is a renowned data scientist with over 10 years of experience in the field. She has worked with major tech companies and has published several research papers on machine learning and artificial intelligence.',
    coursesList: [
      {
        title: 'Advanced Data Science',
        description: 'Learn advanced data science techniques and methodologies.',
        students: 850,
        rating: 4.9,
        image: null
      },
      {
        title: 'Machine Learning Fundamentals',
        description: 'A comprehensive introduction to machine learning concepts.',
        students: 1200,
        rating: 4.7,
        image: null
      }
    ],
    reviews: [
      {
        studentName: 'John Smith',
        studentAvatar: null,
        courseName: 'Advanced Data Science',
        rating: 5,
        date: '2023-05-15',
        content: 'Excellent course! Dr. Johnson explains complex concepts in a very understandable way.'
      },
      {
        studentName: 'Emily Davis',
        studentAvatar: null,
        courseName: 'Machine Learning Fundamentals',
        rating: 4,
        date: '2023-04-20',
        content: 'Very informative course. I learned a lot about machine learning algorithms.'
      }
    ]
  },
  {
    id: 2,
    name: 'Prof. Michael Chen',
    specialization: 'Web Development',
    courses: 8,
    students: 1800,
    rating: 4.6,
    status: 'active',
    avatar: null,
    email: '<EMAIL>',
    bio: 'Professor Michael Chen is a web development expert with extensive experience in modern frontend and backend technologies.',
    coursesList: [
      {
        title: 'Full Stack Web Development',
        description: 'Learn to build complete web applications from frontend to backend.',
        students: 750,
        rating: 4.8,
        image: null
      }
    ],
    reviews: [
      {
        studentName: 'Alex Johnson',
        studentAvatar: null,
        courseName: 'Full Stack Web Development',
        rating: 5,
        date: '2023-06-10',
        content: 'Professor Chen is an excellent instructor. The course content is very practical.'
      }
    ]
  }
]

onMounted(() => {
  // Simulate API call
  setTimeout(() => {
    instructor.value = instructorsData.find(i => i.id === instructorId) || null
    loading.value = false
  }, 500)
})

const goBack = () => {
  router.push('/admin/instructors')
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const toggleStatus = () => {
  if (instructor.value) {
    instructor.value.status = instructor.value.status === 'active' ? 'inactive' : 'active'
  }
}
</script>

<style scoped>
.instructor-profile {
  padding: 2rem;
}

.header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
  margin-top: 5rem;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: var(--tertiary-black);
  border: none;
  color: var(--text-primary);
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
}

.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
}

.spinner {
  border: 4px solid rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  border-top: 4px solid var(--accent-color);
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.profile-header {
  display: flex;
  gap: 2rem;
  background: var(--secondary-black);
  padding: 2rem;
  border-radius: 12px;
  margin-bottom: 2rem;
}

.profile-avatar {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  object-fit: cover;
}

.profile-info {
  flex: 1;
}

.specialization {
  color: var(--text-secondary);
  margin: 0.5rem 0 1rem;
}

.profile-stats {
  display: flex;
  gap: 2rem;
  margin-bottom: 1rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary);
}

.status-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
}

.status-badge.active {
  background-color: rgba(var(--success-color-rgb), 0.2);
  color: var(--success-color);
}

.status-badge.inactive {
  background-color: rgba(var(--danger-color-rgb), 0.2);
  color: var(--danger-color);
}

.status-badge.pending {
  background-color: rgba(var(--warning-color-rgb), 0.2);
  color: var(--warning-color);
}

.profile-tabs {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  border-bottom: 1px solid var(--tertiary-black);
}

.tab-btn {
  padding: 0.75rem 1.5rem;
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  font-weight: 500;
  position: relative;
}

.tab-btn.active {
  color: var(--accent-color);
}

.tab-btn.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 2px;
  background: var(--accent-color);
}

.info-card {
  background: var(--secondary-black);
  padding: 1.5rem;
  border-radius: 12px;
  margin-bottom: 1.5rem;
}

.info-card h3 {
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1.2rem;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.courses-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.course-card {
  background: var(--secondary-black);
  border-radius: 12px;
  overflow: hidden;
}

.course-image {
  width: 100%;
  height: 160px;
  object-fit: cover;
}

.course-content {
  padding: 1.5rem;
}

.course-description {
  color: var(--text-secondary);
  margin: 0.5rem 0 1rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.course-stats {
  display: flex;
  gap: 1rem;
  color: var(--text-secondary);
  margin-bottom: 1rem;
}

.view-course-btn {
  width: 100%;
  padding: 0.75rem;
  background: var(--accent-color);
  color: var(--primary-black);
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
}

.reviews-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.review-item {
  background: var(--secondary-black);
  padding: 1.5rem;
  border-radius: 12px;
}

.review-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.student-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.student-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.course-name {
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin: 0.25rem 0 0;
}

.review-rating {
  text-align: right;
}

.date {
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin-top: 0.25rem;
}

.review-content {
  color: var(--text-primary);
  line-height: 1.5;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  background: var(--secondary-black);
  border-radius: 12px;
  text-align: center;
}

.empty-state i {
  font-size: 3rem;
  color: var(--text-secondary);
  margin-bottom: 1rem;
}

.action-card {
  background: var(--secondary-black);
  padding: 1.5rem;
  border-radius: 12px;
  margin-bottom: 1.5rem;
}

.status-text {
  font-weight: 500;
}

.status-text.active {
  color: var(--success-color);
}

.status-text.inactive {
  color: var(--danger-color);
}

.status-toggle-btn {
  margin-top: 1rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
}

.status-toggle-btn.deactivate {
  background: var(--danger-color);
  color: white;
}

.status-toggle-btn.activate {
  background: var(--success-color);
  color: white;
}

.admin-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.action-btn {
  padding: 0.75rem 1.5rem;
  border: none;}
 
</style>