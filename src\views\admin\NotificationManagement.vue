<template>
  <div class="notification-management">
    <h1>Notification Management</h1>
    
    <div class="notification-channels">
      <div class="channel-card">
        <h3>Live Class Notifications</h3>
        <form @submit.prevent="sendLiveClassNotification">
          <div class="form-group">
            <label for="liveClassCourse">Course</label>
            <select id="liveClassCourse" v-model="liveClassForm.courseId" required>
              <option v-for="course in courses" :key="course.id" :value="course.id">
                {{ course.title }}
              </option>
            </select>
          </div>
          <div class="form-group">
            <label for="liveClassTitle">Title</label>
            <input id="liveClassTitle" v-model="liveClassForm.title" required />
          </div>
          <div class="form-group">
            <label for="liveClassMessage">Message</label>
            <textarea id="liveClassMessage" v-model="liveClassForm.message" required></textarea>
          </div>
          <button type="submit" class="btn btn-primary">Send Notification</button>
        </form>
      </div>
      
      <div class="channel-card">
        <h3>Course Update Notifications</h3>
        <form @submit.prevent="sendCourseUpdate">
          <div class="form-group">
            <label for="courseUpdateCourse">Course</label>
            <select id="courseUpdateCourse" v-model="courseUpdateForm.courseId" required>
              <option v-for="course in courses" :key="course.id" :value="course.id">
                {{ course.title }}
              </option>
            </select>
          </div>
          <div class="form-group">
            <label for="courseUpdateTitle">Title</label>
            <input id="courseUpdateTitle" v-model="courseUpdateForm.title" required />
          </div>
          <div class="form-group">
            <label for="courseUpdateMessage">Message</label>
            <textarea id="courseUpdateMessage" v-model="courseUpdateForm.message" required></textarea>
          </div>
          <button type="submit" class="btn btn-primary">Send Notification</button>
        </form>
      </div>
      
      <div class="channel-card">
        <h3>Private Message</h3>
        <form @submit.prevent="sendPrivateMessage">
          <div class="form-group">
            <label for="privateMessageUser">Recipient</label>
            <select id="privateMessageUser" v-model="privateMessageForm.userId" required>
              <option v-for="user in users" :key="user.id" :value="user.id">
                {{ user.username }}
              </option>
            </select>
          </div>
          <div class="form-group">
            <label for="privateMessageText">Message</label>
            <textarea id="privateMessageText" v-model="privateMessageForm.message" required></textarea>
          </div>
          <button type="submit" class="btn btn-primary">Send Message</button>
        </form>
      </div>
      
      <div class="channel-card">
        <h3>Grade Update Notifications</h3>
        <form @submit.prevent="sendGradeUpdate">
          <div class="form-group">
            <label for="gradeUpdateStudent">Student</label>
            <select id="gradeUpdateStudent" v-model="gradeUpdateForm.userId" required>
              <option v-for="user in students" :key="user.id" :value="user.id">
                {{ user.username }}
              </option>
            </select>
          </div>
          <div class="form-group">
            <label for="gradeUpdateCourse">Course</label>
            <select id="gradeUpdateCourse" v-model="gradeUpdateForm.courseId" required>
              <option v-for="course in courses" :key="course.id" :value="course.id">
                {{ course.title }}
              </option>
            </select>
          </div>
          <div class="form-group">
            <label for="gradeUpdateGrade">Grade</label>
            <input id="gradeUpdateGrade" v-model="gradeUpdateForm.grade" required />
          </div>
          <button type="submit" class="btn btn-primary">Send Notification</button>
        </form>
      </div>
    </div>
    
    <div class="notification-history">
      <h2>Recent Notifications</h2>
      <div class="notification-filters">
        <button 
          v-for="filter in filters" 
          :key="filter.id" 
          :class="['filter-btn', { active: currentFilter === filter.id }]"
          @click="currentFilter = filter.id"
        >
          {{ filter.name }}
        </button>
      </div>
      
      <div class="notifications-list">
        <div 
          v-for="notification in filteredNotifications" 
          :key="notification.id" 
          class="notification-item"
        >
          <div class="notification-icon">
            <i :class="notification.icon"></i>
          </div>
          <div class="notification-content">
            <div class="notification-header">
              <h3>{{ notification.title }}</h3>
              <span class="notification-time">{{ notification.time }}</span>
            </div>
            <p class="notification-message">{{ notification.message }}</p>
            <div class="notification-recipient" v-if="notification.user">
              Sent to: {{ notification.user.username }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import axios from 'axios';

// Form data
const liveClassForm = ref({
  courseId: '',
  title: '',
  message: ''
});

const courseUpdateForm = ref({
  courseId: '',
  title: '',
  message: ''
});

const privateMessageForm = ref({
  userId: '',
  message: ''
});

const gradeUpdateForm = ref({
  userId: '',
  courseId: '',
  grade: ''
});

// Data for dropdowns
const courses = ref([]);
const users = ref([]);
const students = ref([]);

// Notification history
const notifications = ref([]);
const currentFilter = ref('all');

const filters = ref([
  { id: 'all', name: 'All' },
  { id: 'live_class', name: 'Live Class' },
  { id: 'course_update', name: 'Course Updates' },
  { id: 'private_message', name: 'Private Messages' },
  { id: 'grade_update', name: 'Grade Updates' }
]);

const filteredNotifications = computed(() => {
  if (currentFilter.value === 'all') return notifications.value;
  return notifications.value.filter(n => n.notification_type === currentFilter.value);
});

// Fetch data
onMounted(async () => {
  try {
    // Fetch courses
    const coursesResponse = await axios.get('/api/courses/');
    courses.value = coursesResponse.data;
    
    // Fetch users
    const usersResponse = await axios.get('/api/users/');
    users.value = usersResponse.data;
    
    // Fetch students (users with student role)
    const studentsResponse = await axios.get('/api/users/?role=student');
    students.value = studentsResponse.data;
    
    // Fetch recent notifications
    const notificationsResponse = await axios.get('/api/notifications/admin/');
    notifications.value = notificationsResponse.data;
  } catch (error) {
    console.error('Failed to fetch data:', error);
  }
});

// Send notifications
const sendLiveClassNotification = async () => {
  try {
    await axios.post('/api/notifications/send_live_class/', {
      course_id: liveClassForm.value.courseId,
      title: liveClassForm.value.title,
      message: liveClassForm.value.message
    });
    
    // Reset form
    liveClassForm.value = {
      courseId: '',
      title: '',
      message: ''
    };
    
    // Refresh notifications
    const response = await axios.get('/api/notifications/admin/');
    notifications.value = response.data;
    
    alert('Live class notification sent successfully!');
  } catch (error) {
    console.error('Failed to send notification:', error);
    alert('Failed to send notification. Please try again.');
  }
};

const sendCourseUpdate = async () => {
  try {
    await axios.post('/api/notifications/send_course_update/', {
      course_id: courseUpdateForm.value.courseId,
      title: courseUpdateForm.value.title,
      message: courseUpdateForm.value.message
    });
    
    // Reset form
    courseUpdateForm.value = {
      courseId: '',
      title: '',
      message: ''
    };
    
    // Refresh notifications
    const response = await axios.get('/api/notifications/admin/');
    notifications.value = response.data;
    
    alert('Course update notification sent successfully!');
  } catch (error) {
    console.error('Failed to send notification:', error);
    alert('Failed to send notification. Please try again.');
  }
};

const sendPrivateMessage = async () => {
  try {
    await axios.post('/api/notifications/send_private_message/', {
      user_id: privateMessageForm.value.userId,
      message: privateMessageForm.value.message
    });
    
    // Reset form
    privateMessageForm.value = {
      userId: '',
      message: ''
    };
    
    // Refresh notifications
    const response = await axios.get('/api/notifications/admin/');
    notifications.value = response.data;
    
    alert('Private message sent successfully!');
  } catch (error) {
    console.error('Failed to send message:', error);
    alert('Failed to send message. Please try again.');
  }
};

const sendGradeUpdate = async () => {
  try {
    await axios.post('/api/notifications/send_grade_update/', {
      user_id: gradeUpdateForm.value.userId,
      course_id: gradeUpdateForm.value.courseId,
      grade: gradeUpdateForm.value.grade
    });
    
    // Reset form
    gradeUpdateForm.value = {
      userId: '',
      courseId: '',
      grade: ''
    };
    
    // Refresh notifications
    const response = await axios.get('/api/notifications/admin/');
    notifications.value = response.data;
    
    alert('Grade update notification sent successfully!');
  } catch (error) {
    console.error('Failed to send notification:', error);
    alert('Failed to send notification. Please try again.');
  }
};
</script>

<style scoped>
.notification-management {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

h1, h2, h3 {
  margin-bottom: 1.5rem;
}

.notification-channels {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.channel-card {
  background: var(--card-bg);
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.form-group {
  margin-bottom: 1rem;
}

label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

input, select, textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background: var(--input-bg);
  color: var(--text-primary);
}

textarea {
  min-height: 100px;
  resize: vertical;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  margin-top: 1rem;
}

.btn-primary:hover {
  background: var(--primary-dark);
}

.notification-filters {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
}

.filter-btn {
  padding: 0.5rem 1rem;
  border: none;
  background: var(--secondary-bg);
  color: var(--text-primary);
  border-radius: 4px;
  cursor: pointer;
}

.filter-btn.active {
  background: var(--primary-color);
  color: white;
}

.notifications-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.notification-item {
  display: flex;
  background: var(--card-bg);
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.notification-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--primary-light);
  color: var(--primary-color);
  border-radius: 50%;
  margin-right: 1rem;
}

.notification-content {
  flex: 1;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.notification-header h3 {
  margin: 0;
  font-size: 1rem;
}

.notification-time {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.notification-message {
  margin: 0;
  color: var(--text-secondary);
}

.notification-recipient {
  margin-top: 0.5rem;
  font-size: 0.8rem;
  color: var(--text-secondary);
}
</style>
