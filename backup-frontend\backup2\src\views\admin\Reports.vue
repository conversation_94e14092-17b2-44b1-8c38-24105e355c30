<template>
  <div class="admin-reports">
    <div class="header">
      <h1>Analytics & Reports</h1>
      <div class="date-filter">
        <select v-model="timeRange">
          <option value="7">Last 7 days</option>
          <option value="30">Last 30 days</option>
          <option value="90">Last 90 days</option>
          <option value="365">Last year</option>
        </select>
      </div>
    </div>

    <div class="reports-grid">
      <!-- Revenue Stats -->
      <div class="report-card">
        <h2>Revenue Overview</h2>
        <div class="stats-grid">
          <div class="stat-item">
            <span class="stat-label">Total Revenue</span>
            <span class="stat-value">${{ revenueStats.total }}</span>
            <span class="stat-change" :class="{ positive: revenueStats.growth > 0 }">
              {{ revenueStats.growth }}%
            </span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Average Order Value</span>
            <span class="stat-value">${{ revenueStats.averageOrder }}</span>
          </div>
        </div>
      </div>

      <!-- User Stats -->
      <div class="report-card">
        <h2>User Statistics</h2>
        <div class="stats-grid">
          <div class="stat-item">
            <span class="stat-label">New Users</span>
            <span class="stat-value">{{ userStats.new }}</span>
            <span class="stat-change" :class="{ positive: userStats.growth > 0 }">
              {{ userStats.growth }}%
            </span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Active Users</span>
            <span class="stat-value">{{ userStats.active }}</span>
          </div>
        </div>
      </div>

      <!-- Course Stats -->
      <div class="report-card">
        <h2>Course Analytics</h2>
        <div class="stats-grid">
          <div class="stat-item">
            <span class="stat-label">New Enrollments</span>
            <span class="stat-value">{{ courseStats.enrollments }}</span>
            <span class="stat-change" :class="{ positive: courseStats.growth > 0 }">
              {{ courseStats.growth }}%
            </span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Completion Rate</span>
            <span class="stat-value">{{ courseStats.completionRate }}%</span>
          </div>
        </div>
      </div>

      <!-- Popular Courses Table -->
      <div class="report-card full-width">
        <h2>Top Performing Courses</h2>
        <table class="data-table">
          <thead>
            <tr>
              <th>Course Name</th>
              <th>Instructor</th>
              <th>Enrollments</th>
              <th>Revenue</th>
              <th>Rating</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="course in topCourses" :key="course.id">
              <td>{{ course.name }}</td>
              <td>{{ course.instructor }}</td>
              <td>{{ course.enrollments }}</td>
              <td>${{ course.revenue }}</td>
              <td>{{ course.rating }}/5</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const timeRange = ref('30')

const revenueStats = ref({
  total: '125,430',
  growth: 12.5,
  averageOrder: '79.99'
})

const userStats = ref({
  new: 1234,
  growth: 8.3,
  active: 45678
})

const courseStats = ref({
  enrollments: 789,
  growth: 15.7,
  completionRate: 67
})

const topCourses = ref([
  {
    id: 1,
    name: 'Advanced Data Science',
    instructor: 'John Smith',
    enrollments: 456,
    revenue: '34,567',
    rating: 4.8
  },
  {
    id: 2,
    name: 'Web Development Bootcamp',
    instructor: 'Sarah Johnson',
    enrollments: 389,
    revenue: '29,145',
    rating: 4.7
  },
  {
    id: 3,
    name: 'Machine Learning Fundamentals',
    instructor: 'Michael Chen',
    enrollments: 345,
    revenue: '25,890',
    rating: 4.9
  },
  {
    id: 4,
    name: 'Digital Marketing Mastery',
    instructor: 'Emma Davis',
    enrollments: 298,
    revenue: '22,350',
    rating: 4.6
  },
  {
    id: 5,
    name: 'Python Programming',
    instructor: 'David Wilson',
    enrollments: 276,
    revenue: '20,700',
    rating: 4.8
  }
])
</script>

<style scoped>
.admin-reports {
  padding: 2rem;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  margin-top: 5rem;
}

.date-filter select {
  padding: 0.5rem 1rem;
  border-radius: 4px;
  background: var(--secondary-black);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.reports-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.report-card {
  background: var(--secondary-black);
  border-radius: 8px;
  padding: 1.5rem;
}

.report-card h2 {
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  font-size: 1.25rem;
}

.full-width {
  grid-column: 1 / -1;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.stat-label {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
}

.stat-change {
  font-size: 0.875rem;
  color: #ff4444;
}

.stat-change.positive {
  color: #00C851;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th,
.data-table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.data-table th {
  color: var(--text-secondary);
  font-weight: 600;
}

.data-table td {
  color: var(--text-primary);
}

@media (max-width: 768px) {
  .reports-grid {
    grid-template-columns: 1fr;
  }

  .data-table {
    display: block;
    overflow-x: auto;
  }
}
</style>
