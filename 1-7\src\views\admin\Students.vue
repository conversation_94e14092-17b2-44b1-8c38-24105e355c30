<template>
  <div class="admin-students">
    <div class="header">
      <h1>Student Management</h1>
      <div class="filters">
        <select v-model="statusFilter" class="filter-select">
          <option value="all">All Students</option>
          <option value="active">Active</option>
          <option value="inactive">Inactive</option>
        </select>
        <input 
          type="text" 
          v-model="searchQuery" 
          placeholder="Search students..." 
          class="search-input"
        >
      </div>
    </div>

    <div class="stats-row">
      <div class="stat-card">
        <h3>Total Students</h3>
        <p class="stat-value">{{ stats.totalStudents }}</p>
      </div>
      <div class="stat-card">
        <h3>Active Learners</h3>
        <p class="stat-value">{{ stats.activeLearners }}</p>
      </div>
      <div class="stat-card">
        <h3>Course Completions</h3>
        <p class="stat-value">{{ stats.completions }}</p>
      </div>
      <div class="stat-card">
        <h3>Avg. Course Progress</h3>
        <p class="stat-value">{{ stats.avgProgress }}%</p>
      </div>
    </div>

    <!-- Loading indicator -->
    <div v-if="studentStore.loading" class="loading-indicator">
      <span>Loading students...</span>
    </div>
    
    <!-- Students table with transition -->
    <div class="students-table" v-else>
      <TransitionGroup name="fade" tag="table">
        <thead key="thead">
          <tr>
            <th>Student</th>
            <th>Enrolled Courses</th>
            <th>Progress</th>
            <th>Last Active</th>
            <th>Status</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody key="tbody">
          <tr v-for="student in filteredStudents" :key="student.id">
            <td>
              <div class="student-info">
                <img :src="student.avatar || '/default-avatar.png'" :alt="student.name" class="student-avatar">
                <div>
                  <div class="student-name">{{ student.name }}</div>
                  <div class="student-email">{{ student.email }}</div>
                </div>
              </div>
            </td>
            <td>{{ student.enrolledCourses }}</td>
            <td>
              <div class="progress-bar">
                <div 
                  class="progress" 
                  :style="{ width: `${student.progress}%` }"
                ></div>
              </div>
              <span class="progress-text">{{ student.progress }}%</span>
            </td>
            <td>{{ formatDate(student.lastActive) }}</td>
            <td>
              <span :class="['status-badge', student.status]">
                {{ student.status }}
              </span>
            </td>
            <td>
              <div class="action-buttons">
                <button @click="viewDetails(student.id)" class="view-btn">
                  <i class="fas fa-eye"></i>
                </button>
                <button 
                  @click="toggleStatus(student.id)" 
                  :class="['status-btn', student.status]"
                >
                  <i class="fas fa-power-off"></i>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </TransitionGroup>
      
      <!-- Pagination controls -->
      <div class="pagination">
        <button 
          @click="changePage(studentStore.pagination.currentPage - 1)"
          :disabled="studentStore.pagination.currentPage === 1"
          class="pagination-btn"
        >
          <i class="fas fa-chevron-left"></i>
        </button>
        
        <span class="pagination-info">
          Page {{ studentStore.pagination.currentPage }} of {{ studentStore.pagination.totalPages }}
        </span>
        
        <button 
          @click="changePage(studentStore.pagination.currentPage + 1)"
          :disabled="studentStore.pagination.currentPage === studentStore.pagination.totalPages"
          class="pagination-btn"
        >
          <i class="fas fa-chevron-right"></i>
        </button>
      </div>
    </div>
  </div>

  <!-- Student Details Modal -->
  <div v-if="showStudentModal" class="modal-overlay" @click="closeStudentModal">
    <div class="modal-content" @click.stop>
      <div class="modal-header">
        <h2>Student Details</h2>
        <button class="close-btn" @click="closeStudentModal">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="modal-body" v-if="selectedStudent">
        <div class="student-profile">
          <div class="profile-header">
            <img :src="selectedStudent.avatar || '/default-avatar.png'" :alt="selectedStudent.name" class="student-avatar-large">
            <div class="profile-info">
              <input v-model="editedStudent.name" class="edit-input name-input" placeholder="Student Name">
              <input v-model="editedStudent.email" class="edit-input email-input" placeholder="Email Address">
              <div class="status-toggle">
                <span>Status:</span>
                <div class="toggle-switch">
                  <input 
                    type="checkbox" 
                    id="statusToggle" 
                    :checked="editedStudent.status === 'active'"
                    @change="editedStudent.status = $event.target.checked ? 'active' : 'inactive'"
                  >
                  <label for="statusToggle"></label>
                </div>
                <span :class="['status-text', editedStudent.status]">{{ editedStudent.status }}</span>
              </div>
            </div>
          </div>
          
          <div class="profile-stats">
            <div class="stat-item">
              <span class="stat-label">Enrolled Courses</span>
              <span class="stat-value">{{ selectedStudent.enrolledCourses }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Progress</span>
              <div class="progress-container">
                <div class="progress-bar">
                  <div class="progress" :style="{ width: `${selectedStudent.progress}%` }"></div>
                </div>
                <span class="progress-text">{{ selectedStudent.progress }}%</span>
              </div>
            </div>
            <div class="stat-item">
              <span class="stat-label">Last Active</span>
              <span class="stat-value">{{ formatDate(selectedStudent.lastActive) }}</span>
            </div>
          </div>
          
          <div class="action-buttons">
            <button class="save-btn" @click="saveStudentChanges">
              <i class="fas fa-save"></i> Save Changes
            </button>
            <button class="delete-btn" @click="confirmDeleteStudent">
              <i class="fas fa-trash"></i> Delete Student
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Delete Confirmation Modal -->
  <div v-if="showDeleteModal" class="modal-overlay" @click="showDeleteModal = false">
    <div class="modal-content delete-modal" @click.stop>
      <div class="modal-header">
        <h2>Confirm Delete</h2>
        <button class="close-btn" @click="showDeleteModal = false">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="modal-body">
        <p>Are you sure you want to delete this student? This action cannot be undone.</p>
        <div class="confirm-buttons">
          <button class="cancel-btn" @click="showDeleteModal = false">
            Cancel
          </button>
          <button class="confirm-delete-btn" @click="deleteStudent">
            Delete Student
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useStudentStore } from '../../stores/studentStore'
import { useToast } from '../../composables/useToast'

const router = useRouter()
const studentStore = useStudentStore()
const { showToast } = useToast()

const statusFilter = ref('all')
const searchQuery = ref('')
const isLoading = ref(false)
const showStudentModal = ref(false)
const showDeleteModal = ref(false)
const selectedStudent = ref(null)
const editedStudent = ref(null)

// Computed property to get filtered students from the store
const filteredStudents = computed(() => 
  studentStore.getFilteredStudents(statusFilter.value, searchQuery.value)
)

// Computed property to access stats from the store
const stats = computed(() => studentStore.stats)

// Load students and stats when component mounts
onMounted(async () => {
  try {
    console.log('Students component mounted, fetching data...')
    
    // First fetch the students list with any existing filters
    await studentStore.fetchStudents({
      status: statusFilter.value,
      search: searchQuery.value
    })
    console.log('Students fetched successfully')
    
    // Then fetch detailed stats
    await studentStore.fetchStudentStats()
    console.log('Student stats fetched successfully')
    
    // Sync the component's filter values with the store's current filters
    statusFilter.value = studentStore.currentFilters.status
    searchQuery.value = studentStore.currentFilters.search
    
    // Log the final stats for debugging
    console.log('Final stats after both API calls:', studentStore.stats)
  } catch (error) {
    console.error('Failed to initialize student data:', error)
    showToast('Failed to load student data', 'error')
  }
})

// Add a watcher to update stats when filters change
watch([statusFilter, searchQuery], async (newValues, oldValues) => {
  // Only refetch if values actually changed
  if (newValues[0] === oldValues[0] && newValues[1] === oldValues[1]) return
  
  try {
    console.log('Filters changed, refetching students with:', {
      status: statusFilter.value,
      search: searchQuery.value
    })
    
    // When filters change, refetch students with the new filters
    await studentStore.fetchStudents({
      status: statusFilter.value,
      search: searchQuery.value
    })
    
    // Optionally refetch stats if they should change based on filters
    // await studentStore.fetchStudentStats()
  } catch (error) {
    console.error('Error applying filters:', error)
    showToast('Failed to apply filters', 'error')
  }
})

const changePage = async (newPage) => {
  // Set local loading state to true before changing page
  isLoading.value = true
  
  // Add a small delay to ensure smooth transition
  await new Promise(resolve => setTimeout(resolve, 50))
  
  // Change the page
  await studentStore.changePage(newPage)
  
  // Reset loading state
  isLoading.value = false
}

const formatDate = (date) => {
  if (!date) return 'Never'
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const viewDetails = (studentId) => {
  // Find the student in the store
  const student = studentStore.students.find(s => s.id === studentId)
  if (student) {
    // Set the selected student and create a copy for editing
    selectedStudent.value = { ...student }
    editedStudent.value = { ...student }
    showStudentModal.value = true
  }
}

const closeStudentModal = () => {
  showStudentModal.value = false
  selectedStudent.value = null
  editedStudent.value = null
}

const saveStudentChanges = async () => {
  if (!editedStudent.value) return
  
  try {
    await studentStore.updateStudent({
      id: editedStudent.value.id,
      name: editedStudent.value.name,
      email: editedStudent.value.email,
      is_active: editedStudent.value.status === 'active'
    })
    
    // Update the selected student with the edited values
    selectedStudent.value = { ...editedStudent.value }
    
    // Show success toast
    showToast(`${editedStudent.value.name}'s record has been updated`, 'success')
    
    // Close the modal
    closeStudentModal()
  } catch (error) {
    console.error('Failed to update student:', error)
    showToast('Failed to update student record', 'error')
  }
}

const confirmDeleteStudent = () => {
  showDeleteModal.value = true
}

const deleteStudent = async () => {
  if (!selectedStudent.value) return
  
  try {
    await studentStore.deleteStudent(selectedStudent.value.id)
    
    // Close both modals
    showDeleteModal.value = false
    showStudentModal.value = false
    
    // Show success message
    alert('Student deleted successfully')
  } catch (error) {
    console.error('Failed to delete student:', error)
    alert('Failed to delete student')
  }
}

const toggleStatus = async (studentId) => {
  try {
    // Find the student to get their name
    const student = studentStore.students.find(s => s.id === studentId)
    if (!student) {
      console.error('Student not found:', studentId)
      showToast('Student not found', 'error')
      return
    }
    
    // Get the current status before toggling
    const currentStatus = student.status
    const newStatus = currentStatus === 'active' ? 'inactive' : 'active'
    
    console.log(`Toggling student ${student.name} (${studentId}) from ${currentStatus} to ${newStatus}`)
    
    // Toggle the status
    await studentStore.toggleStudentStatus(studentId)
    
    // Show success toast with student name and new status
    showToast(
      `${student.name}'s status changed to ${newStatus}`, 
      'success'
    )
  } catch (error) {
    console.error('Failed to toggle student status:', error)
    showToast('Failed to update student status', 'error')
  }
}

const refreshStudents = async () => {
  try {
    console.log('Refreshing students with current filters:', {
      status: statusFilter.value,
      search: searchQuery.value
    })
    
    await studentStore.fetchStudents({
      status: statusFilter.value,
      search: searchQuery.value
    }, studentStore.pagination.currentPage)
    
    await studentStore.fetchStudentStats()
    
    showToast('Student data refreshed', 'success')
  } catch (error) {
    console.error('Failed to refresh students:', error)
    showToast('Failed to refresh student data', 'error')
  }
}
</script>

<style scoped>
.admin-students {
  padding: 2rem;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  margin-top: 5rem;
}

h1 {
  margin: 0;
  font-size: 1.75rem;
  font-weight: 600;
}

.filters {
  display: flex;
  gap: 1rem;
}

.filter-select,
.search-input {
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background: var(--secondary-black);
  color: var(--text-primary);
}

.stats-row {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: var(--secondary-black);
  padding: 1.5rem;
  border-radius: 8px;
  text-align: center;
}

.stat-value {
  font-size: 2rem;
  font-weight: bold;
  color: var(--accent-color);
  margin-top: 0.5rem;
}

.students-table {
  background: var(--secondary-black);
  border-radius: 8px;
  overflow: hidden;
}

table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  position: relative;
}

th, td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

th {
  background: var(--tertiary-black);
  font-weight: 600;
}

.student-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.student-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.student-name {
  font-weight: 500;
}

.student-email {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.progress-bar {
  height: 8px;
  background: var(--tertiary-black);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.25rem;
}

.progress {
  height: 100%;
  background: var(--accent-color);
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.status-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.9rem;
  text-transform: capitalize;
}

.status-badge.active {
  background: rgba(0, 255, 148, 0.1);
  color: #00FF94;
}

.status-badge.inactive {
  background: rgba(255, 69, 58, 0.1);
  color: #FF453A;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
}

.view-btn,
.status-btn {
  padding: 0.5rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: opacity 0.3s;
}

.view-btn {
  background: var(--tertiary-black);
  color: var(--text-primary);
}

.status-btn.active {
  background: rgba(255, 69, 58, 0.1);
  color: #FF453A;
}

.status-btn.inactive {
  background: rgba(0, 255, 148, 0.1);
  color: #00FF94;
}

.view-btn:hover,
.status-btn:hover {
  opacity: 0.8;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .admin-students {
    padding: 1rem;
  }
  
  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .filters {
    width: 100%;
  }
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 1.5rem;
  padding: 1rem;
}

.pagination-btn {
  background: var(--secondary-black);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 0.5rem 0.75rem;
  cursor: pointer;
  color: var(--text-primary);
  transition: all 0.3s ease;
}

.pagination-btn:hover:not(:disabled) {
  background: var(--tertiary-black);
  transform: translateY(-2px);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-info {
  margin: 0 1rem;
  color: var(--text-secondary);
}

.loading-indicator {
  text-align: center;
  padding: 2rem;
  background: var(--secondary-black);
  border-radius: 8px;
  margin-top: 1rem;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% { opacity: 0.6; }
  50% { opacity: 1; }
  100% { opacity: 0.6; }
}

/* Transition styles */
.fade-move,
.fade-enter-active,
.fade-leave-active {
  transition: all 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(30px);
}

/* ensure leaving items are taken out of layout flow so that moving
   animations can be calculated correctly */
.fade-leave-active {
  position: absolute;
}

/* Improve table layout for transitions */
tbody tr {
  transition: background-color 0.3s ease;
}

tbody tr:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

/* Modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: var(--secondary-black);
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  animation: modal-appear 0.3s ease-out;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
  margin: 0;
  color: var(--text-primary);
}

.close-btn {
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 1.5rem;
  cursor: pointer;
  transition: color 0.2s;
}

.close-btn:hover {
  color: var(--text-primary);
}

.modal-body {
  padding: 1.5rem;
}

/* Student profile styles */
.student-profile {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.profile-header {
  display: flex;
  gap: 1.5rem;
}

.student-avatar-large {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid var(--primary-color);
}

.profile-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.edit-input {
  background-color: var(--tertiary-black);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 0.75rem;
  color: var(--text-primary);
  font-size: 1rem;
  width: 100%;
  transition: border-color 0.2s;
}

.edit-input:focus {
  border-color: var(--primary-color);
  outline: none;
}

.name-input {
  font-size: 1.25rem;
  font-weight: 600;
}

.status-toggle {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-top: 0.5rem;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-switch label {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 34px;
}

.toggle-switch label:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

.toggle-switch input:checked + label {
  background-color: var(--primary-color);
}

.toggle-switch input:checked + label:before {
  transform: translateX(26px);
}

.status-text {
  font-weight: 500;
}

.status-text.active {
  color: var(--success-color);
}

.status-text.inactive {
  color: var(--danger-color);
}

.profile-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  background-color: var(--tertiary-black);
  border-radius: 8px;
  padding: 1rem;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.stat-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.stat-value {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.progress-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1rem;
}

.save-btn, .delete-btn {
  padding: 0.75rem 1.25rem;
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.save-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
}

.save-btn:hover {
  background-color: var(--primary-color-dark);
}

.delete-btn {
  background-color: transparent;
  color: var(--danger-color);
  border: 1px solid var(--danger-color);
}

.delete-btn:hover {
  background-color: var(--danger-color);
  color: white;
}

/* Delete confirmation modal */
.delete-modal {
  max-width: 400px;
}

.confirm-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1.5rem;
}

.cancel-btn, .confirm-delete-btn {
  padding: 0.75rem 1.25rem;
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.cancel-btn {
  background-color: transparent;
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
}

.cancel-btn:hover {
  background-color: var(--tertiary-black);
}

.confirm-delete-btn {
  background-color: var(--danger-color);
  color: white;
  border: none;
}

.confirm-delete-btn:hover {
  background-color: var(--danger-color-dark);
}

@keyframes modal-appear {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>








