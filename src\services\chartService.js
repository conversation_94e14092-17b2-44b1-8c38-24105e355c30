import apiClient from './apiClient'

// API URL for chart data
const API_URL = '/api/analytics/'

// Flag to control mock API usage
const USE_MOCK_API = true

export const chartService = {
  // Get revenue data for charts
  async getRevenueData(timeRange = '30') {
    if (USE_MOCK_API) {
      return Promise.resolve({
        data: {
          labels: generateDateLabels(timeRange),
          datasets: [
            {
              label: 'Revenue',
              data: generateRandomData(parseInt(timeRange), 500, 2000),
              borderColor: '#4CAF50',
              backgroundColor: 'rgba(76, 175, 80, 0.2)',
              tension: 0.4
            }
          ]
        }
      })
    }
    
    try {
      const response = await apiClient.get(`${API_URL}revenue/?time_range=${timeRange}`)
      return response
    } catch (error) {
      console.error('Error fetching revenue data:', error)
      throw error
    }
  },
  
  // Get enrollment data for charts
  async getEnrollmentData(timeRange = '30') {
    if (USE_MOCK_API) {
      return Promise.resolve({
        data: {
          labels: generateDateLabels(timeRange),
          datasets: [
            {
              label: 'Enrollments',
              data: generateRandomData(parseInt(timeRange), 10, 50),
              borderColor: '#2196F3',
              backgroundColor: 'rgba(33, 150, 243, 0.2)',
              tension: 0.4
            }
          ]
        }
      })
    }
    
    try {
      const response = await apiClient.get(`${API_URL}enrollments/?time_range=${timeRange}`)
      return response
    } catch (error) {
      console.error('Error fetching enrollment data:', error)
      throw error
    }
  },
  
  // Get user registration data for charts
  async getUserRegistrationData(timeRange = '30') {
    if (USE_MOCK_API) {
      return Promise.resolve({
        data: {
          labels: generateDateLabels(timeRange),
          datasets: [
            {
              label: 'New Users',
              data: generateRandomData(parseInt(timeRange), 5, 30),
              borderColor: '#FF9800',
              backgroundColor: 'rgba(255, 152, 0, 0.2)',
              tension: 0.4
            }
          ]
        }
      })
    }
    
    try {
      const response = await apiClient.get(`${API_URL}users/?time_range=${timeRange}`)
      return response
    } catch (error) {
      console.error('Error fetching user registration data:', error)
      throw error
    }
  },
  
  // Get course completion data for charts
  async getCourseCompletionData() {
    if (USE_MOCK_API) {
      return Promise.resolve({
        data: {
          labels: ['Completed', 'In Progress', 'Not Started'],
          datasets: [
            {
              data: [65, 25, 10],
              backgroundColor: ['#4CAF50', '#FFC107', '#F44336'],
              borderWidth: 0
            }
          ]
        }
      })
    }
    
    try {
      const response = await apiClient.get(`${API_URL}course-completion/`)
      return response
    } catch (error) {
      console.error('Error fetching course completion data:', error)
      throw error
    }
  },
  
  // Get category distribution data for charts
  async getCategoryDistributionData() {
    if (USE_MOCK_API) {
      return Promise.resolve({
        data: {
          labels: ['Programming', 'Design', 'Business', 'Marketing', 'Data Science', 'Other'],
          datasets: [
            {
              data: [30, 20, 15, 15, 10, 10],
              backgroundColor: [
                '#4CAF50', '#2196F3', '#FF9800', 
                '#9C27B0', '#F44336', '#607D8B'
              ],
              borderWidth: 0
            }
          ]
        }
      })
    }
    
    try {
      const response = await apiClient.get(`${API_URL}categories/`)
      return response
    } catch (error) {
      console.error('Error fetching category distribution data:', error)
      throw error
    }
  }
}

// Helper function to generate date labels for charts
function generateDateLabels(days) {
  const labels = []
  const today = new Date()
  
  for (let i = parseInt(days) - 1; i >= 0; i--) {
    const date = new Date(today)
    date.setDate(date.getDate() - i)
    labels.push(date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }))
  }
  
  return labels
}

// Helper function to generate random data for charts
function generateRandomData(count, min, max) {
  return Array.from({ length: count }, () => 
    Math.floor(Math.random() * (max - min + 1)) + min
  )
}