<template>
  <div class="admin-reports">
    <div class="header">
      <h1>Analytics & Reports</h1>
      <div class="date-filter">
        <select v-model="timeRange" @change="updateTimeRange(timeRange)">
          <option value="7">Last 7 days</option>
          <option value="30">Last 30 days</option>
          <option value="90">Last 90 days</option>
          <option value="365">Last year</option>
        </select>
      </div>
    </div>

    <div class="reports-grid">
      <!-- Revenue Stats with Chart -->
      <div class="report-card">
        <h2>Revenue Overview</h2>
        <div class="stats-grid">
          <div class="stat-item">
            <span class="stat-label">Total Revenue</span>
            <span class="stat-value">${{ revenueStats.total }}</span>
            <span class="stat-change" :class="{ positive: revenueStats.growth > 0 }">
              {{ revenueStats.growth }}%
            </span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Average Order Value</span>
            <span class="stat-value">${{ revenueStats.averageOrder }}</span>
          </div>
        </div>
        <div class="chart-wrapper">
          <BaseChart 
            type="line" 
            :data="revenueData" 
            :loading="getLoadingState('revenue')"
            :error="getErrorState('revenue')"
            @retry="fetchSpecificChart('revenue')"
            height="180px"
            :options="{
              plugins: {
                legend: { display: false }
              }
            }"
          />
        </div>
      </div>

      <!-- User Stats with Chart -->
      <div class="report-card">
        <h2>User Analytics</h2>
        <div class="stats-grid">
          <div class="stat-item">
            <span class="stat-label">New Users</span>
            <span class="stat-value">{{ userStats.new }}</span>
            <span class="stat-change" :class="{ positive: userStats.growth > 0 }">
              {{ userStats.growth }}%
            </span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Active Users</span>
            <span class="stat-value">{{ userStats.active }}</span>
          </div>
        </div>
        <div class="chart-wrapper">
          <BaseChart 
            type="line" 
            :data="userRegistrationData" 
            :loading="getLoadingState('userRegistration')"
            :error="getErrorState('userRegistration')"
            @retry="fetchSpecificChart('userRegistration')"
            height="180px"
            :options="{
              plugins: {
                legend: { display: false }
              }
            }"
          />
        </div>
      </div>

      <!-- Course Stats with Chart -->
      <div class="report-card">
        <h2>Course Analytics</h2>
        <div class="stats-grid">
          <div class="stat-item">
            <span class="stat-label">New Enrollments</span>
            <span class="stat-value">{{ courseStats.enrollments }}</span>
            <span class="stat-change" :class="{ positive: courseStats.growth > 0 }">
              {{ courseStats.growth }}%
            </span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Completion Rate</span>
            <span class="stat-value">{{ courseStats.completionRate }}%</span>
          </div>
        </div>
        <div class="chart-wrapper">
          <BaseChart 
            type="line" 
            :data="enrollmentData" 
            :loading="getLoadingState('enrollment')"
            :error="getErrorState('enrollment')"
            @retry="fetchSpecificChart('enrollment')"
            height="180px"
            :options="{
              plugins: {
                legend: { display: false }
              }
            }"
          />
        </div>
      </div>

      <!-- Category Distribution Chart -->
      <div class="report-card">
        <h2>Course Categories</h2>
        <div class="chart-wrapper">
          <BaseChart 
            type="doughnut" 
            :data="categoryDistributionData" 
            :loading="getLoadingState('categoryDistribution')"
            :error="getErrorState('categoryDistribution')"
            @retry="fetchSpecificChart('categoryDistribution')"
            height="220px"
            :options="{ 
              cutout: '70%',
              plugins: { 
                legend: { 
                  position: 'right',
                  labels: { 
                    boxWidth: 12,
                    padding: 15
                  }
                } 
              } 
            }"
          />
        </div>
      </div>

      <!-- Course Completion Chart -->
      <div class="report-card">
        <h2>Course Completion</h2>
        <div class="chart-wrapper">
          <BaseChart 
            type="pie" 
            :data="courseCompletionData" 
            :loading="getLoadingState('courseCompletion')"
            :error="getErrorState('courseCompletion')"
            @retry="fetchSpecificChart('courseCompletion')"
            height="220px"
          />
        </div>
      </div>

      <!-- Popular Courses Table -->
      <div class="report-card full-width">
        <h2>Top Performing Courses</h2>
        <table class="data-table">
          <thead>
            <tr>
              <th>Course Name</th>
              <th>Instructor</th>
              <th>Enrollments</th>
              <th>Revenue</th>
              <th>Rating</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="course in topCourses" :key="course.id">
              <td>{{ course.name }}</td>
              <td>{{ course.instructor }}</td>
              <td>{{ course.enrollments }}</td>
              <td>${{ course.revenue }}</td>
              <td>{{ course.rating }}/5</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import BaseChart from '../../components/charts/BaseChart.vue'
import { useCharts } from '../../composables/useCharts'

// Get chart data and methods from the composable
const { 
  timeRange,
  revenueData,
  enrollmentData,
  userRegistrationData,
  courseCompletionData,
  categoryDistributionData,
  updateTimeRange,
  fetchSpecificChart,
  getLoadingState,
  getErrorState
} = useCharts({ autoFetch: true })

// Static data for stats
const revenueStats = ref({
  total: '125,430',
  growth: 12.5,
  averageOrder: '79.99'
})

const userStats = ref({
  new: 1234,
  growth: 8.3,
  active: 45678
})

const courseStats = ref({
  enrollments: 789,
  growth: 15.7,
  completionRate: 67
})

const topCourses = ref([
  {
    id: 1,
    name: 'Advanced Data Science',
    instructor: 'John Smith',
    enrollments: 456,
    revenue: '34,567',
    rating: 4.8
  },
  {
    id: 2,
    name: 'Web Development Bootcamp',
    instructor: 'Sarah Johnson',
    enrollments: 389,
    revenue: '29,145',
    rating: 4.7
  },
  {
    id: 3,
    name: 'Machine Learning Fundamentals',
    instructor: 'Michael Chen',
    enrollments: 345,
    revenue: '25,890',
    rating: 4.9
  },
  {
    id: 4,
    name: 'Digital Marketing Mastery',
    instructor: 'Emma Davis',
    enrollments: 298,
    revenue: '22,350',
    rating: 4.6
  },
  {
    id: 5,
    name: 'Python Programming',
    instructor: 'David Wilson',
    enrollments: 276,
    revenue: '20,700',
    rating: 4.8
  }
])
</script>

<style scoped>
.admin-reports {
  padding: 2rem;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.date-filter select {
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #ddd;
  background-color: white;
}

.reports-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

.report-card {
  background:#2d2d2d;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.full-width {
  grid-column: 1 / -1;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  margin-bottom: 20px;
}

.stat-item {
  display: flex;
  flex-direction: column;
}

.stat-label {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 5px;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: bold;
}

.stat-change {
  font-size: 0.9rem;
  color: #f44336;
}

.stat-change.positive {
  color: #4caf50;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th, .data-table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
}

.data-table th {
  color: var(--text-secondary);
  font-weight: 600;
  font-size: 0.9rem;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  background-color: rgba(0, 0, 0, 0.2);
}

.data-table td {
  color: var(--text-primary);
  font-size: 0.95rem;
  font-weight: 500;
}

.data-table tr:hover td {
  background-color: rgba(255, 255, 255, 0.03);
}

/* Add alternating row colors for better readability */
.data-table tbody tr:nth-child(even) {
  background-color: rgba(255, 255, 255, 0.02);
}

.chart-wrapper {
  margin-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.05);
  padding-top: 1.2rem;
  height: auto;
}

@media (max-width: 768px) {
  .reports-grid {
    grid-template-columns: 1fr;
  }

  .data-table {
    display: block;
    overflow-x: auto;
  }
}
</style>



