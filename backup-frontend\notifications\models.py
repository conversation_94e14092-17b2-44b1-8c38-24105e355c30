from django.db import models
from django.contrib.auth import get_user_model

User = get_user_model()

class Notification(models.Model):
    NOTIFICATION_TYPES = (
        ('live_class', 'Live Class'),
        ('course_update', 'Course Update'),
        ('private_message', 'Private Message'),
        ('grade_update', 'Grade Update'),
    )
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='notifications')
    notification_type = models.CharField(max_length=20, choices=NOTIFICATION_TYPES)
    title = models.Char<PERSON>ield(max_length=255)
    message = models.TextField()
    icon = models.Char<PERSON>ield(max_length=50, default='fas fa-bell')
    read = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-created_at']