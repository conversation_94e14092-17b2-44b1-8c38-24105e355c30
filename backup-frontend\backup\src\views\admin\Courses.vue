<template>
  <div class="admin-courses">
    <div class="header">
      <h1>Course Management</h1>
      <div class="filters">
        <select v-model="statusFilter" class="filter-select">
          <option value="all">All Courses</option>
          <option value="active">Active</option>
          <option value="pending">Pending Review</option>
          <option value="draft">Draft</option>
        </select>
        <input 
          type="text" 
          v-model="searchQuery" 
          placeholder="Search courses..." 
          class="search-input"
        >
      </div>
    </div>

    <div class="courses-grid">
      <div
        v-for="course in filteredCourses"
        :key="course.id"
        class="course-card"
        @click="viewCourseLessons(course.id)"
      >
        <img :src="course.image || '/default-course.jpg'" :alt="course.name" class="course-image">
        <div class="course-content">
          <div class="course-header">
            <h3>{{ course.name }}</h3>
            <span :class="['status-badge', course.status]">{{ course.status }}</span>
          </div>
          <p class="instructor">{{ course.instructor }}</p>
          <div class="course-stats">
            <span><i class="fas fa-users"></i> {{ course.enrolled }} students</span>
            <span><i class="fas fa-star"></i> {{ course.rating }}</span>
          </div>
          <div class="course-actions">
            <button @click.stop="reviewCourse(course.id)" class="review-btn">
              <i class="fas fa-eye"></i> Review
            </button>
            <button @click.stop="toggleCourseStatus(course.id)" :class="['status-btn', course.status]">
              {{ course.status === 'active' ? 'Deactivate' : 'Activate' }}
            </button>
            <button @click.stop="deleteCourse(course.id)" class="delete-btn">
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const statusFilter = ref('all')
const searchQuery = ref('')

const courses = ref([
  {
    id: 1,
    name: 'Advanced Data Science',
    instructor: 'John Doe',
    image: '/course1.jpg',
    enrolled: 450,
    rating: 4.9,
    status: 'active',
    lessons: [
      'Introduction to Data Science',
      'Data Cleaning Techniques',
      'Feature Engineering',
      'Model Evaluation'
    ]
  },
  {
    id: 2,
    name: 'Web Development Fundamentals',
    instructor: 'Jane Smith',
    image: '/course2.jpg',
    enrolled: 380,
    rating: 4.7,
    status: 'pending',
    lessons: [
      'HTML Basics',
      'CSS Styling',
      'JavaScript Fundamentals',
      'Responsive Design'
    ]
  },
  {
    id: 3,
    name: 'Machine Learning Basics',
    instructor: 'Mike Johnson',
    image: '/course3.jpg',
    enrolled: 0,
    rating: 0,
    status: 'draft',
    lessons: [
      'Introduction to ML',
      'Supervised Learning',
      'Unsupervised Learning'
    ]
  }
])

const filteredCourses = computed(() => {
  return courses.value
    .filter(course => {
      if (statusFilter.value === 'all') return true
      return course.status === statusFilter.value
    })
    .filter(course => {
      if (!searchQuery.value) return true
      return course.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
             course.instructor.toLowerCase().includes(searchQuery.value.toLowerCase())
    })
})

const viewCourseLessons = (courseId) => {
  router.push(`/admin/courses/${courseId}/lessons`)
}

const reviewCourse = (courseId) => {
  // Implement course review logic
  console.log('Reviewing course:', courseId)
}

const toggleCourseStatus = (courseId) => {
  const course = courses.value.find(c => c.id === courseId)
  if (course) {
    course.status = course.status === 'active' ? 'inactive' : 'active'
  }
}

const deleteCourse = (courseId) => {
  if (confirm('Are you sure you want to delete this course?')) {
    courses.value = courses.value.filter(course => course.id !== courseId)
  }
}
</script>

<style scoped>
.admin-courses {
  padding: 2rem;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  margin-top: 5rem;
}

.filters {
  display: flex;
  gap: 1rem;
}

.filter-select,
.search-input {
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background: var(--secondary-black);
  color: var(--text-primary);
}

.courses-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.course-card {
  background: var(--secondary-black);
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid var(--border-color);
  transition: transform 0.3s ease;
  cursor: pointer;
}

.course-card:hover {
  transform: translateY(-5px);
}

.course-image {
  width: 100%;
  height: 160px;
  object-fit: cover;
}

.course-content {
  padding: 1.5rem;
}

.course-header {
  display: flex;
  justify-content: space-between;
  align-items: start;
  margin-bottom: 0.5rem;
}

.status-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-badge.active {
  background: var(--success-color);
  color: var(--text-primary);
}

.status-badge.pending {
  background: var(--warning-color);
  color: var(--text-primary);
}

.status-badge.draft {
  background: var(--tertiary-black);
  color: var(--text-secondary);
}

.status-badge.inactive {
  background: var(--error-color);
  color: var(--text-primary);
}

.instructor {
  color: var(--text-secondary);
  margin-bottom: 1rem;
}

.course-stats {
  display: flex;
  gap: 1rem;
  color: var(--text-secondary);
  margin-bottom: 1rem;
}

.course-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;
}

.course-actions button {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.review-btn {
  background: var(--accent-color);
  color: var(--primary-black);
}

.status-btn {
  background: var(--success-color);
  color: var(--text-primary);
}

.status-btn.inactive {
  background: var(--warning-color);
}

.delete-btn {
  background: var(--error-color);
  color: var(--text-primary);
}

.course-actions button:hover {
  opacity: 0.9;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .admin-courses {
    padding: 1rem;
  }
  
  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .filters {
    width: 100%;
    flex-direction: column;
  }
  
  .course-actions {
    flex-wrap: wrap;
  }
  
  .course-actions button {
    flex: 1;
    min-width: 80px;
    justify-content: center;
  }
}
</style>



