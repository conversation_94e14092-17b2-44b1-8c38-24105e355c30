<template>
  <div class="course-details">
    <div class="header">
      <div class="course-actions">
        <button @click="goBack" class="back-btn">
          <i class="fas fa-arrow-left"></i> Back to Courses
        </button>
        <div class="action-buttons">
          <button @click="editCourse" class="edit-btn">
            <i class="fas fa-edit"></i> Edit Course
          </button>
          <button @click="goToLessons" class="lessons-btn">
            <i class="fas fa-list"></i> Manage Lessons
          </button>
          <button @click="goToReviews" class="reviews-btn">
            <i class="fas fa-star"></i> View Reviews
          </button>
        </div>
      </div>
      
      <!-- Add navigation tabs -->
      <div class="course-tabs">
        <div class="tab active">Overview</div>
        <div class="tab" @click="goToLessons">Lessons</div>
        <div class="tab" @click="goToReviews">Reviews</div>
        <div class="tab" @click="goToStudents">Students</div>
      </div>
    </div>

    <!-- Loading state -->
    <div v-if="loading" class="loading-container">
      <div class="spinner"></div>
      <p>Loading course details...</p>
    </div>

    <!-- Error state -->
    <div v-else-if="error" class="error-container">
      <p>{{ error }}</p>
      <button @click="fetchCourseDetails" class="retry-btn">Retry</button>
    </div>

    <!-- Course details -->
    <div v-else-if="course" class="course-content">
      <div class="course-header">
        <img 
          :src="course.thumbnail || course.image || '/default-course.jpg'" 
          :alt="course.title" 
          class="course-image"
          @error="handleImageError"
        >
        <div class="course-info">
          <h1>{{ course.title || course.name }}</h1>
          <p class="subtitle">{{ course.subtitle }}</p>
          <div class="meta">
            <span class="instructor"><i class="fas fa-user"></i> {{ course.instructor }}</span>
            <span class="level"><i class="fas fa-signal"></i> {{ course.level }}</span>
            <span class="students"><i class="fas fa-users"></i> {{ course.enrolled || 0 }} students</span>
            <span class="rating"><i class="fas fa-star"></i> {{ course.rating || 0 }}</span>
            <span :class="['status', course.status]">{{ course.status }}</span>
          </div>
          <p class="price">${{ course.price || 0 }}</p>
        </div>
      </div>

      <div class="course-description">
        <h2>Description</h2>
        <p>{{ course.description || 'No description available.' }}</p>
      </div>

      <div class="course-lessons">
        <div class="section-header">
          <h2>Lessons</h2>
          <button @click="goToLessons" class="view-lessons-btn">
            <i class="fas fa-list"></i> Manage Lessons
          </button>
        </div>
        <div v-if="course.lessons && course.lessons.length > 0" class="lessons-list">
          <div v-for="(lesson, index) in course.lessons" :key="index" class="lesson-item">
            <span class="lesson-number">{{ index + 1 }}</span>
            <span class="lesson-title">{{ lesson }}</span>
          </div>
        </div>
        <p v-else class="no-lessons">No lessons available for this course.</p>
      </div>
    </div>

    <!-- Empty state -->
    <div v-else class="empty-container">
      <p>Course not found.</p>
      <button @click="goBack" class="back-btn">Back to Courses</button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useCourseStore } from '../../stores/courseStore'

const route = useRoute()
const router = useRouter()
const courseStore = useCourseStore()
const courseId = route.params.id
const course = ref(null)
const loading = ref(true)
const error = ref(null)

const fetchCourseDetails = async () => {
  loading.value = true
  error.value = null
  
  try {
    // First check if the course is already in the store
    if (courseStore.courses.length > 0) {
      course.value = courseStore.courses.find(c => c.id == courseId)
    }
    
    // If not found in store, fetch it
    if (!course.value) {
      await courseStore.fetchCourse(courseId)
      course.value = courseStore.currentCourse
    }
  } catch (err) {
    console.error('Failed to fetch course details:', err)
    error.value = 'Failed to load course details. Please try again.'
  } finally {
    loading.value = false
  }
}

const goBack = () => {
  router.push('/admin/courses')
}

const editCourse = () => {
  router.push(`/admin/courses/edit/${courseId}`)
}

const goToLessons = () => {
  router.push(`/admin/courses/${courseId}/lessons`)
}

const goToReviews = () => {
  router.push(`/admin/courses/${courseId}/reviews`)
}

const goToStudents = () => {
  router.push(`/admin/courses/${courseId}/students`)
}

const handleImageError = (event) => {
  event.target.src = '/default-course.jpg'
}

onMounted(() => {
  fetchCourseDetails()
})
</script>

<style scoped>
.course-details {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: var(--secondary-black);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.back-btn:hover {
  background: var(--tertiary-black);
}

.actions {
  display: flex;
  gap: 1rem;
}

.edit-btn {
  background: var(--accent-color);
  color: var(--primary-black);
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.course-header {
  display: flex;
  gap: 2rem;
  margin-bottom: 2rem;
}

.course-image {
  width: 300px;
  height: 200px;
  object-fit: cover;
  border-radius: 12px;
  flex-shrink: 0;
}

.course-info {
  flex: 1;
}

.course-info h1 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-size: 2rem;
}

.subtitle {
  color: var(--text-secondary);
  margin-bottom: 1rem;
  font-size: 1.2rem;
}

.meta {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1rem;
}

.meta span {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary);
}

.status {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-weight: 500;
}

.status.active {
  background: var(--success-color);
  color: var(--text-primary);
}

.status.draft {
  background: var(--tertiary-black);
  color: var(--text-secondary);
}

.status.pending {
  background: var(--warning-color);
  color: var(--text-primary);
}

.status.inactive {
  background: var(--error-color);
  color: var(--text-primary);
}

.price {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--accent-color);
}

.course-description, .course-lessons {
  background: var(--secondary-black);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  border: 1px solid var(--border-color);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.view-lessons-btn {
  background: var(--accent-color);
  color: var(--primary-black);
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.lessons-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.lesson-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem;
  background: var(--tertiary-black);
  border-radius: 8px;
}

.lesson-number {
  background: var(--accent-color);
  color: var(--primary-black);
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
}

.no-lessons {
  color: var(--text-secondary);
  font-style: italic;
}

/* Loading and error states */
.loading-container, .error-container, .empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
}

.spinner {
  border: 4px solid rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  border-top: 4px solid var(--accent-color);
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.retry-btn {
  margin-top: 1rem;
  background: var(--accent-color);
  color: var(--primary-black);
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
}

@media (max-width: 768px) {
  .course-header {
    flex-direction: column;
  }
  
  .course-image {
    width: 100%;
    height: 200px;
  }
}

/* Add styles for the tabs */
.course-tabs {
  display: flex;
  margin-top: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.tab {
  padding: 0.75rem 1.5rem;
  cursor: pointer;
  font-weight: 500;
  position: relative;
  transition: color 0.3s;
}

.tab:hover {
  color: var(--accent-color);
}

.tab.active {
  color: var(--accent-color);
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: var(--accent-color);
}
</style>

