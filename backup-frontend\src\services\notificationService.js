class NotificationService {
  constructor() {
    this.socket = null;
    this.connected = false;
    this.callbacks = {
      onMessage: null,
      onConnect: null,
      onDisconnect: null
    };
  }

  connect() {
    // Close any existing connection
    if (this.socket) {
      this.socket.close();
    }

    // Create WebSocket connection
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${protocol}//${window.location.host}/ws/notifications/`;
    
    this.socket = new WebSocket(wsUrl);
    
    this.socket.onopen = () => {
      this.connected = true;
      if (this.callbacks.onConnect) {
        this.callbacks.onConnect();
      }
    };
    
    this.socket.onmessage = (event) => {
      const data = JSON.parse(event.data);
      if (data.type === 'notification' && this.callbacks.onMessage) {
        this.callbacks.onMessage(data.notification);
      }
    };
    
    this.socket.onclose = () => {
      this.connected = false;
      if (this.callbacks.onDisconnect) {
        this.callbacks.onDisconnect();
      }
      
      // Try to reconnect after 5 seconds
      setTimeout(() => this.connect(), 5000);
    };
  }
  
  markAsRead(notificationId) {
    if (this.connected) {
      this.socket.send(JSON.stringify({
        type: 'mark_read',
        notification_id: notificationId
      }));
    }
  }
  
  setCallbacks(callbacks) {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }
  
  disconnect() {
    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }
  }
}

export default new NotificationService();