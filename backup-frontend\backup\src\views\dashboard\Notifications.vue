<template>
  <div class="notifications">
    <h1>Notifications</h1>

    <div class="notification-filters">
      <button 
        v-for="filter in filters" 
        :key="filter.id"
        :class="['filter-btn', { active: currentFilter === filter.id }]"
        @click="currentFilter = filter.id"
      >
        {{ filter.name }}
      </button>
    </div>

    <div class="notifications-list">
      <div 
        v-for="notification in filteredNotifications" 
        :key="notification.id" 
        :class="['notification-item', { unread: !notification.read }]"
      >
        <div class="notification-icon">
          <i :class="notification.icon"></i>
        </div>
        <div class="notification-content">
          <div class="notification-header">
            <h3>{{ notification.title }}</h3>
            <span class="notification-time">{{ notification.time }}</span>
          </div>
          <p class="notification-message">{{ notification.message }}</p>
        </div>
        <button 
          v-if="!notification.read" 
          class="mark-read-btn"
          @click="markAsRead(notification.id)"
        >
          <i class="fas fa-check"></i>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const currentFilter = ref('all')

const filters = ref([
  { id: 'all', name: 'All' },
  { id: 'unread', name: 'Unread' },
  { id: 'courses', name: 'Courses' },
  { id: 'announcements', name: 'Announcements' }
])

const notifications = ref([
  {
    id: 1,
    type: 'courses',
    title: 'New Course Content Available',
    message: 'New lessons have been added to "Advanced Data Analysis"',
    time: '1 hour ago',
    read: false,
    icon: 'fas fa-book'
  },
  {
    id: 2,
    type: 'announcements',
    title: 'Platform Maintenance',
    message: 'Scheduled maintenance on Saturday, 10 PM EST',
    time: '2 hours ago',
    read: true,
    icon: 'fas fa-bell'
  },
  {
    id: 3,
    type: 'courses',
    title: 'Assignment Due Soon',
    message: 'Remember to submit your final project by Friday',
    time: '1 day ago',
    read: false,
    icon: 'fas fa-calendar'
  }
])

const filteredNotifications = computed(() => {
  if (currentFilter.value === 'all') return notifications.value
  if (currentFilter.value === 'unread') return notifications.value.filter(n => !n.read)
  return notifications.value.filter(n => n.type === currentFilter.value)
})

const markAsRead = (id) => {
  const notification = notifications.value.find(n => n.id === id)
  if (notification) {
    notification.read = true
  }
}
</script>

<style scoped>
.notifications {
  max-width: 800px;
  margin: 0 auto;
}

.notification-filters {
  display: flex;
  gap: 1rem;
  margin: 2rem 0;
}

.filter-btn {
  padding: 0.5rem 1rem;
  border: none;
  background: var(--secondary-black);
  color: var(--text-primary);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-btn.active {
  background: var(--accent-color);
  color: var(--primary-black);
}

.notification-item {
  display: flex;
  align-items: flex-start;
  padding: 1rem;
  background: var(--secondary-black);
  border-radius: 8px;
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

.notification-item.unread {
  background: var(--tertiary-black);
  border-left: 4px solid var(--accent-color);
}

.notification-icon {
  width: 40px;
  height: 40px;
  background: var(--tertiary-black);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  color: var(--accent-color);
}

.notification-content {
  flex: 1;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.notification-time {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.notification-message {
  color: var(--text-secondary);
}

.mark-read-btn {
  background: transparent;
  border: none;
  color: var(--accent-color);
  cursor: pointer;
  padding: 0.5rem;
}

.mark-read-btn:hover {
  color: var(--text-primary);
}
</style>