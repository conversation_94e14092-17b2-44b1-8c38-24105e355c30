<template>
  <div class="admin-instructors">
    <div class="header">
      <h1>Instructors</h1>
      <div class="filters">
        <select v-model="statusFilter" class="filter-select">
          <option value="all">All Instructors</option>
          <option value="active">Active</option>
          <option value="inactive">Inactive</option>
        </select>
        <input 
          type="text" 
          v-model="searchQuery" 
          placeholder="Search instructors..." 
          class="search-input"
        >
        <button @click="refreshInstructors" class="refresh-btn" :disabled="instructorStore.loading">
          <i class="fas fa-sync-alt" :class="{ 'fa-spin': instructorStore.loading }"></i>
          Refresh
        </button>
      </div>
    </div>

    <div class="stats-row">
      <div class="stat-card">
        <div class="stat-value">{{ instructorStore.stats.total }}</div>
        <div class="stat-label">Total Instructors</div>
      </div>
      <div class="stat-card">
        <div class="stat-value">{{ instructorStore.stats.active }}</div>
        <div class="stat-label">Active Instructors</div>
      </div>
      <div class="stat-card">
        <div class="stat-value">{{ instructorStore.stats.pending }}</div>
        <div class="stat-label">Pending Approval</div>
      </div>
      <div class="stat-card">
        <div class="stat-value">{{ instructorStore.stats.avgRating.toFixed(1) }}</div>
        <div class="stat-label">Average Rating</div>
      </div>
    </div>

    <!-- Loading indicator -->
    <div v-if="instructorStore.loading" class="loading-indicator">
      <div class="spinner"></div>
      <span>Loading instructors...</span>
    </div>
    
    <!-- Error state -->
    <div v-else-if="instructorStore.error" class="error-container">
      <i class="fas fa-exclamation-triangle"></i>
      <p>{{ instructorStore.error }}</p>
      <button @click="refreshInstructors" class="retry-btn">Retry</button>
    </div>
    
    <!-- Empty state -->
    <div v-else-if="filteredInstructors.length === 0" class="empty-container">
      <p>No instructors found. {{ searchQuery ? 'Try a different search term.' : 'Add your first instructor to get started.' }}</p>
    </div>

    <div v-else class="instructors-grid">
      <div v-for="instructor in filteredInstructors" :key="instructor.id" class="instructor-card">
        <img :src="instructor.avatar || '/default-avatar.png'" :alt="instructor.name" class="instructor-avatar">
        <div class="instructor-info">
          <h3>{{ instructor.name }}</h3>
          <p class="instructor-title">{{ instructor.specialization }}</p>
          <div class="instructor-stats">
            <span><i class="fas fa-book"></i> {{ instructor.courses }} courses</span>
            <span><i class="fas fa-users"></i> {{ instructor.students }} students</span>
            <span><i class="fas fa-star"></i> {{ instructor.rating.toFixed(1) }}</span>
          </div>
          <div class="instructor-actions">
            <button @click="viewProfile(instructor.id)" class="view-btn">
              View Profile
            </button>
            <button 
              @click="toggleStatus(instructor.id)" 
              :class="['status-btn', instructor.status]"
            >
              {{ instructor.status === 'active' ? 'Deactivate' : 'Activate' }}
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Pagination -->
    <div v-if="instructorStore.pagination.totalPages > 1" class="pagination">
      <button 
        @click="instructorStore.changePage(instructorStore.pagination.currentPage - 1)"
        :disabled="instructorStore.pagination.currentPage === 1"
        class="pagination-btn"
      >
        <i class="fas fa-chevron-left"></i>
      </button>
      <span class="pagination-info">
        Page {{ instructorStore.pagination.currentPage }} of {{ instructorStore.pagination.totalPages }}
      </span>
      <button 
        @click="instructorStore.changePage(instructorStore.pagination.currentPage + 1)"
        :disabled="instructorStore.pagination.currentPage === instructorStore.pagination.totalPages"
        class="pagination-btn"
      >
        <i class="fas fa-chevron-right"></i>
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useInstructorStore } from '../../stores/instructorStore'
import { useToast } from '../../composables/useToast'

const router = useRouter()
const instructorStore = useInstructorStore()
const { showToast } = useToast()

const statusFilter = ref('all')
const searchQuery = ref('')

// Computed property for filtered instructors
const filteredInstructors = computed(() => {
  return instructorStore.instructors
    .filter(instructor => {
      if (statusFilter.value === 'all') return true
      return instructor.status === statusFilter.value
    })
    .filter(instructor => {
      if (!searchQuery.value) return true
      return instructor.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
             instructor.specialization.toLowerCase().includes(searchQuery.value.toLowerCase())
    })
})

// Function to view instructor profile
const viewProfile = (instructorId) => {
  router.push(`/admin/instructors/${instructorId}`)
}

// Function to toggle instructor status
const toggleStatus = async (instructorId) => {
  try {
    // Find the instructor to get their name
    const instructor = instructorStore.instructors.find(i => i.id === instructorId)
    if (!instructor) {
      console.error('Instructor not found:', instructorId)
      showToast('Instructor not found', 'error')
      return
    }
    
    // Get the current status before toggling
    const currentStatus = instructor.status
    const newStatus = currentStatus === 'active' ? 'inactive' : 'active'
    
    console.log(`Toggling instructor ${instructor.name} (${instructorId}) from ${currentStatus} to ${newStatus}`)
    
    // Toggle the status
    await instructorStore.toggleInstructorStatus(instructorId)
    
    // Show success toast with instructor name and new status
    showToast(
      `${instructor.name}'s status changed to ${newStatus}`, 
      'success'
    )
  } catch (error) {
    console.error('Failed to toggle instructor status:', error)
    showToast('Failed to update instructor status', 'error')
  }
}

// Function to refresh instructors
const refreshInstructors = async () => {
  try {
    console.log('Refreshing instructors with current filters:', {
      status: statusFilter.value,
      search: searchQuery.value
    })
    
    await instructorStore.fetchInstructors({
      status: statusFilter.value,
      search: searchQuery.value
    }, instructorStore.pagination.currentPage)
    
    await instructorStore.fetchInstructorStats()
    
    showToast('Instructor data refreshed', 'success')
  } catch (error) {
    console.error('Failed to refresh instructors:', error)
    showToast('Failed to refresh instructor data', 'error')
  }
}

// Watch for filter changes
watch([statusFilter, searchQuery], async (newValues, oldValues) => {
  // Only refetch if values actually changed
  if (newValues[0] === oldValues[0] && newValues[1] === oldValues[1]) return
  
  try {
    console.log('Filters changed, refetching instructors with:', {
      status: statusFilter.value,
      search: searchQuery.value
    })
    
    // When filters change, refetch instructors with the new filters
    await instructorStore.fetchInstructors({
      status: statusFilter.value,
      search: searchQuery.value
    })
  } catch (error) {
    console.error('Error applying filters:', error)
    showToast('Failed to apply filters', 'error')
  }
})

// Fetch data on component mount
onMounted(async () => {
  try {
    console.log('Instructors component mounted, fetching data...')
    
    // First fetch the instructors list
    await instructorStore.fetchInstructors()
    console.log('Instructors fetched successfully')
    
    // Then fetch detailed stats
    await instructorStore.fetchInstructorStats()
    console.log('Instructor stats fetched successfully')
  } catch (error) {
    console.error('Error fetching instructors or stats:', error)
    showToast('Failed to fetch instructors or stats', 'error')
  }
})
</script>

<style scoped>
.admin-instructors {
  padding: 2rem;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  margin-top: 5rem;
}

.filters {
  display: flex;
  gap: 1rem;
}

.stats-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: var(--secondary-black);
  padding: 1.5rem;
  border-radius: 12px;
  text-align: center;
}

.stat-value {
  font-size: 2rem;
  font-weight: 600;
  color: var(--accent-color);
  margin-top: 0.5rem;
}

.instructors-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.instructor-card {
  background: var(--secondary-black);
  border-radius: 12px;
  padding: 1.5rem;
  display: flex;
  gap: 1.5rem;
}

.instructor-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
}

.instructor-info {
  flex: 1;
}

.instructor-title {
  color: var(--text-secondary);
  margin: 0.5rem 0;
}

.instructor-stats {
  display: flex;
  gap: 1rem;
  margin: 1rem 0;
  color: var(--text-secondary);
}

.instructor-actions {
  display: flex;
  gap: 0.5rem;
}

.instructor-actions button {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
}

.view-btn {
  background: var(--accent-color);
  color: var(--primary-black);
}

.status-btn {
  background: var(--success-color);
  color: var(--text-primary);
}

.status-btn.inactive {
  background: var(--warning-color);
}

.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  margin: 2rem 0;
}

.spinner {
  border: 4px solid var(--secondary-black);
  border-top: 4px solid var(--accent-color);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  margin: 2rem 0;
  color: var(--error-color);
}

.error-container i {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.retry-btn {
  background: var(--accent-color);
  color: var(--primary-black);
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  margin: 2rem 0;
  color: var(--text-secondary);
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 2rem;
}

.pagination-btn {
  background: var(--secondary-black);
  color: var(--primary-black);
  border: none;
  border-radius: 6px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  font-weight: 500;
  margin: 0 0.5rem;
}

.pagination-btn:disabled {
  background: var(--disabled-color);
  cursor: not-allowed;
}

.pagination-info {
  margin: 0 1rem;
  color: var(--text-secondary);
}
</style>




