import { defineStore } from 'pinia';
import axios from 'axios';
import notificationService from '../services/notificationService';

export const useNotificationStore = defineStore('notification', {
  state: () => ({
    notifications: [],
    unreadCount: 0,
    isConnected: false
  }),
  
  getters: {
    unreadNotifications: (state) => state.notifications.filter(n => !n.read),
    getByType: (state) => (type) => state.notifications.filter(n => n.type === type)
  },
  
  actions: {
    async fetchNotifications() {
      try {
        const response = await axios.get('/api/notifications/');
        this.notifications = response.data;
        this.updateUnreadCount();
      } catch (error) {
        console.error('Failed to fetch notifications:', error);
      }
    },
    
    addNotification(notification) {
      // Add to beginning of array
      this.notifications.unshift(notification);
      this.updateUnreadCount();
    },
    
    async markAsRead(notificationId) {
      try {
        await axios.post(`/api/notifications/${notificationId}/mark_read/`);
        
        // Update local state
        const notification = this.notifications.find(n => n.id === notificationId);
        if (notification) {
          notification.read = true;
          this.updateUnreadCount();
        }
        
        // Also notify WebSocket
        notificationService.markAsRead(notificationId);
      } catch (error) {
        console.error('Failed to mark notification as read:', error);
      }
    },
    
    async markAllAsRead() {
      try {
        await axios.post('/api/notifications/mark_all_read/');
        
        // Update local state
        this.notifications.forEach(notification => {
          notification.read = true;
        });
        this.updateUnreadCount();
      } catch (error) {
        console.error('Failed to mark all notifications as read:', error);
      }
    },
    
    updateUnreadCount() {
      this.unreadCount = this.notifications.filter(n => !n.read).length;
    },
    
    setupWebSocket() {
      notificationService.setCallbacks({
        onMessage: (notification) => {
          this.addNotification(notification);
        },
        onConnect: () => {
          this.isConnected = true;
        },
        onDisconnect: () => {
          this.isConnected = false;
        }
      });
      
      notificationService.connect();
    },
    
    cleanupWebSocket() {
      notificationService.disconnect();
    }
  }
});