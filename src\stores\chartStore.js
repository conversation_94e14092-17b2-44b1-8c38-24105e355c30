import { defineStore } from 'pinia'
import { chartService } from '../services/chartService'

export const useChartStore = defineStore('chart', {
  state: () => ({
    revenueData: null,
    enrollmentData: null,
    userRegistrationData: null,
    courseCompletionData: null,
    categoryDistributionData: null,
    loading: {
      revenue: false,
      enrollment: false,
      userRegistration: false,
      courseCompletion: false,
      categoryDistribution: false
    },
    error: {
      revenue: null,
      enrollment: null,
      userRegistration: null,
      courseCompletion: null,
      categoryDistribution: null
    },
    timeRange: '30'
  }),
  
  actions: {
    // Set time range for all charts
    setTimeRange(range) {
      this.timeRange = range
    },
    
    // Fetch revenue data for charts
    async fetchRevenueData() {
      this.loading.revenue = true
      this.error.revenue = null
      
      try {
        const response = await chartService.getRevenueData(this.timeRange)
        this.revenueData = response.data
        return this.revenueData
      } catch (error) {
        this.error.revenue = error.message || 'Failed to fetch revenue data'
        console.error('Error in fetchRevenueData:', error)
        throw error
      } finally {
        this.loading.revenue = false
      }
    },
    
    // Fetch enrollment data for charts
    async fetchEnrollmentData() {
      this.loading.enrollment = true
      this.error.enrollment = null
      
      try {
        const response = await chartService.getEnrollmentData(this.timeRange)
        this.enrollmentData = response.data
        return this.enrollmentData
      } catch (error) {
        this.error.enrollment = error.message || 'Failed to fetch enrollment data'
        console.error('Error in fetchEnrollmentData:', error)
        throw error
      } finally {
        this.loading.enrollment = false
      }
    },
    
    // Fetch user registration data for charts
    async fetchUserRegistrationData() {
      this.loading.userRegistration = true
      this.error.userRegistration = null
      
      try {
        const response = await chartService.getUserRegistrationData(this.timeRange)
        this.userRegistrationData = response.data
        return this.userRegistrationData
      } catch (error) {
        this.error.userRegistration = error.message || 'Failed to fetch user registration data'
        console.error('Error in fetchUserRegistrationData:', error)
        throw error
      } finally {
        this.loading.userRegistration = false
      }
    },
    
    // Fetch course completion data for charts
    async fetchCourseCompletionData() {
      this.loading.courseCompletion = true
      this.error.courseCompletion = null
      
      try {
        const response = await chartService.getCourseCompletionData()
        this.courseCompletionData = response.data
        return this.courseCompletionData
      } catch (error) {
        this.error.courseCompletion = error.message || 'Failed to fetch course completion data'
        console.error('Error in fetchCourseCompletionData:', error)
        throw error
      } finally {
        this.loading.courseCompletion = false
      }
    },
    
    // Fetch category distribution data for charts
    async fetchCategoryDistributionData() {
      this.loading.categoryDistribution = true
      this.error.categoryDistribution = null
      
      try {
        const response = await chartService.getCategoryDistributionData()
        this.categoryDistributionData = response.data
        return this.categoryDistributionData
      } catch (error) {
        this.error.categoryDistribution = error.message || 'Failed to fetch category distribution data'
        console.error('Error in fetchCategoryDistributionData:', error)
        throw error
      } finally {
        this.loading.categoryDistribution = false
      }
    },
    
    // Fetch all chart data at once
    async fetchAllChartData() {
      try {
        await Promise.all([
          this.fetchRevenueData(),
          this.fetchEnrollmentData(),
          this.fetchUserRegistrationData(),
          this.fetchCourseCompletionData(),
          this.fetchCategoryDistributionData()
        ])
        return true
      } catch (error) {
        console.error('Error fetching all chart data:', error)
        return false
      }
    }
  }
})